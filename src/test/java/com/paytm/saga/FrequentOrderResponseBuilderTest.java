package com.paytm.saga;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FrequentOrderResponse;
import com.paytm.saga.dto.builder.FrequentOrderResponseBuilder;
import com.paytm.saga.enums.BillState;
import com.paytm.saga.enums.EventType;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.service.impl.ServiceConfig;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class FrequentOrderResponseBuilderTest {

    @Mock
    private MetricsHelper metricsHelper;

    @Mock
    private ServiceConfig serviceConfig;

    @Mock
    private Recents recent;

    @Mock
    private RecentsPrimaryKey recentsPrimaryKey;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // Setup basic recent mock
        when(recent.getKey()).thenReturn(recentsPrimaryKey);
        when(recent.getChannelId()).thenReturn("WEB");
        when(recent.getConsumerName()).thenReturn("Test User");
        when(recent.getProductId()).thenReturn(12345L);
        when(recent.getEventSource()).thenReturn(Constants.EVENT_SOURCE.SMS);
        when(recentsPrimaryKey.getRechargeNumber()).thenReturn("9999999999");
    }

    @Test
    public void testParseInsuranceCardInfo() {
        // Given
        String insuranceCardInfo = "{\"make\":\"Honda\",\"model\":\"City\",\"vehicleColor\":\"Red\",\"variantId\":\"123\"}";

        // When
        Map<String, Object> result = FrequentOrderResponseBuilder.parseInsuranceCardInfo(insuranceCardInfo, recent);

        // Then
        assertNotNull(result);
        assertEquals("Honda", result.get("make"));
        assertEquals("City", result.get("model"));
        assertEquals("Red", result.get("vehicleColor"));
        assertEquals("123", result.get("variantId"));
    }

    @Test
    public void recentToFrequentOrderResponse_setsAllFieldsCorrectlyForValidRecents() {
        Recents recent = mock(Recents.class);
        MetricsHelper metricsHelper = mock(MetricsHelper.class);
        ServiceConfig serviceConfig = mock(ServiceConfig.class);

        when(recent.getChannelId()).thenReturn("channel1");
        when(recent.getOperator()).thenReturn("LOW_BALANCE");
        when(recent.getConsumerName()).thenReturn("John Doe");
        when(recent.getCylinderAgencyName()).thenReturn("Agency1");
        when(recent.getNickName()).thenReturn("Nick");
        when(recent.getAutomaticStatus()).thenReturn(1);
        when(recent.getAutomaticDate()).thenReturn(new Date());
        when(recent.getAutomaticAmount()).thenReturn(100.0);
        when(recent.getExtra()).thenReturn("{\"type\":\"LOW_BALANCE\",\"low_balance_date\":\"2025-04-01 13:21:06\",\"recon_id\":\"oyY5oQBvYxVTzjSxhY/VUgMwKO0=\",\"user_type\":\"NON_RU\"}");
        when(recent.getNotificationStatus()).thenReturn(1);
        when(recent.getUpdatedAt()).thenReturn(new Date());
        when(recent.getCreatedAt()).thenReturn(new Date());
        when(recent.getProductId()).thenReturn(123L);
        when(recent.getEventSource()).thenReturn(Constants.EVENT_SOURCE.SMS);
        when(recent.getTxnAmount()).thenReturn(200.0);
        when(recent.getTxnTime()).thenReturn(new Date());
        when(recent.getKey()).thenReturn(mock(RecentsPrimaryKey.class));
        when(recent.getKey().getRechargeNumber()).thenReturn("9999900000");
        when(recent.getPayType()).thenReturn("prepaid");

        FrequentOrderResponse response = FrequentOrderResponseBuilder.recentToFrequentOrderResponse(recent, metricsHelper, serviceConfig);

        assertNotNull(response);
        assertEquals("channel1", response.getChannel());
        assertEquals("John Doe", response.getConsumerName());
        assertEquals("Agency1", response.getCylinderAgencyName());
        assertEquals("Nick", response.getNickName());
        assertNotNull(response.getAutomaticDate());
        assertNotNull(response.getDate());
        assertNotNull(response.getCreatedAt());
        assertEquals("123", response.getPid());
        assertEquals(EventType.RECENT, response.getEventType());
        assertNotNull(response.getTxnDate());
        assertEquals("9999900000", response.getRechargeNumber1());
    }

    @Test
    public void recentToFrequentOrderResponse_IsDwhSmsParsingManual_IsFullBill_All_Set() {
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setRechargeNumber("9999999999");
        recents.getKey().setService("mobile");
        recents.getKey().setOperator("airtel");
        recents.getKey().setCustomerId(1234567890L);
        recents.getKey().setPlanBucket("");
        recents.setPayType("prepaid");
        recents.setExtra("{\"isDwhSmsParsingManual\":true}");
        recents.setEventSource( Constants.EVENT_SOURCE.SMS );
        recents.setBillDate(new Date());
        recents.setDueDate(new Date());
        recents.setDueAmount(100.0);
        FrequentOrderResponse response = FrequentOrderResponseBuilder.recentToFrequentOrderResponse(recents, metricsHelper, serviceConfig);
        assertTrue(response.getIsDwhSmsParsingManual());
        assertTrue(response.getIsFullBill());
    }

    @Test
    public void recentToFrequentOrderResponse_IsDwhSmsParsingManual_IsFullBill_All_False() {
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setRechargeNumber("9999999999");
        recents.getKey().setService("mobile");
        recents.getKey().setOperator("airtel");
        recents.getKey().setCustomerId(1234567890L);
        recents.getKey().setPlanBucket("");
        recents.setPayType("prepaid");
        recents.setExtra("{\"isDwhSmsParsingManual\":false}");
        recents.setEventSource( Constants.EVENT_SOURCE.SMS );
        recents.setBillDate(new Date());
        recents.setDueDate(new Date());
        recents.setDueAmount(null); //partial bill
        FrequentOrderResponse response = FrequentOrderResponseBuilder.recentToFrequentOrderResponse(recents, metricsHelper, serviceConfig);
        assertFalse(response.getIsDwhSmsParsingManual());
        assertFalse(response.getIsFullBill());
    }
}