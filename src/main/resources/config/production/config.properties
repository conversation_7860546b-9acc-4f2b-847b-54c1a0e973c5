application.environment = production

#Cassandra Properties
cassandra.keyspace-name.saga=recharge_saga
cassandra.keyspace-name.recent=recent
cassandra.contact-points=recharge_saga_cassandra.prod.paytmdgt.io
saga.cassandra.contact-points=recharge_saga_cassandra.prod.paytmdgt.io
cassandra.port=9042
cassandra.username=user_saga
cassandra.dc.saga=ap-south-1
cassandra.dc.recent=ap-south-1


#JPA Properties for MYSQL
spring.datasource.url=*********************************************************
spring.datasource.username=fs_appuser
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

external.endpoints.reminderMarkAsPaid=http://digitalreminder.prod.paytmdgt.io/v1/markAsPaid
external.endpoints.recentsCRUDUpdate=http://recents-crud.prod.paytmdgt.io/v1/recentupdate/admin
external.endpoints.browsePlanUrl=https://digitalcatalog.paytm.com/dcat/v1/browseplans/
dcat.categoryUrl=https://digitalcatalog.paytm.com/dcat/v1/category/
#external.endpoints.pgSavedCards=https://pgp-internal-lb.paytm.in/savedcardservice/savedcardOpenAPIService/v1/savedcardsByUserId
external.endpoints.pgSavedCards=https://pgp-internal-alb.paytm.com/savedcardservice/user/cards
external.endpoints.smsCards=http://digitalreminder.prod.paytmdgt.io/v2/bill-nonPaytm/getBill
external.endpoints.plansUrl=http://digitalcatalog-internal.paytmdgt.io/rps/v1/plans/
external.endpoints.elasticSearch=http://internal-digital-searchstats-alb-2006557837.ap-south-1.elb.amazonaws.com/recharge_mis/_search

external.endpoints.itemV2Url=http://order-internal.paytm.com/v2/admin/items_v2.json
external.endpoints.billsSyncMarkAsPaid=http://digital-billsync.prod.paytmdgt.io/dbs/v1/markaspaid

#Kafka Properties
oms.kafka.bootstrapServers=ordkafka-v35-internal.paytm.com:9092
oms.kafka.topicName=order_update
oms.kafka.consumerGroup=recharges_saga_consumer

#Kafka Properties
scratch.card.kafka.bootstrapServers=chat-prod-txn-kafka01.paytmdgt.io:9092,chat-prod-txn-kafka02.paytmdgt.io:9092,chat-prod-txn-kafka03.paytmdgt.io:9092,chat-prod-txn-kafka04.paytmdgt.io:9092,chat-prod-txn-kafka05.paytmdgt.io:9092
scratch.card.kafka.topicName=scratchcards_1
scratch.card.kafka.consumerGroup=recharges_saga_consumer

#Kafka Properties
plan.validity.kafka.bootstrapServers=10.4.33.90:9092,10.4.33.198:9092,10.4.33.158:9092
plan.validity.kafka.topicName=PLAN_VALIDITY_MAXWELL
plan.validity.kafka.consumerGroup=recharges_saga_consumer

#Kafka Properties
reminder.kafka.bootstrapServers=10.4.33.39:9092,10.4.33.171:9092,10.4.33.148:9092
reminder.kafka.topicName=REMINDER_MAXWELL
reminder.kafka.consumerGroup=recharges_saga_consumer


automatic.kafka.bootstrapServers=10.4.33.90:9092,10.4.33.198:9092,10.4.33.158:9092
automatic.kafka.topicName=AUTOMATIC_SUBS_DATA
automatic.kafka.consumerGroup=recharges_saga_consumer

cdcreminder.kafka.bootstrapServers=10.4.33.39:9092,10.4.33.171:9092,10.4.33.148:9092
cdcreminder.kafka.topicName_SMS=saga.reminder.bills_non_paytm
cdcreminder.kafka.topicName_P2P=saga.reminder.bills_nonru
cdcreminder.kafka.topicNameSmsRecovery=CDC_RECOVERY
cdcreminder.kafka.consumerGroup=cdc_saga_reminder

#billerAccount Kafka Properties
billeraccount.kafka.bootstrapServers=10.4.33.171:9092,10.4.33.148:9092,10.4.33.39:9092
billeraccount.kafka.topicNames=BILLER_ACCOUNT_DATA,BILLER_ACCOUNT_ADDITION_DATA
billeraccount.kafka.consumerGroup=recharges_saga_consumer

#PREPAID_BILLS Kafka Properties
prepaid.bills.kafka.bootstrapServers=10.4.33.39:9092,10.4.33.171:9092,10.4.33.148:9092
prepaid.bills.kafka.topicName=PREPAID_BILLS_MAXWELL
prepaid.bills.kafka.consumerGroup=saga_prepaid_bills

#Kafka Properties
evictcache.kafka.bootstrapServers=10.4.33.90:9092,10.4.33.198:9092,10.4.33.158:9092
evictcache.kafka.topicName=EVICT_CACHE
evictcache.kafka.consumerGroup=abc
evictcache.kafka.clientId=evict_cache_producer


cdcrollback.kafka.bootstrapServers=10.4.33.39:9092,10.4.33.171:9092,10.4.33.148:9092
cdcrollback.kafka.topicName=CDC_ROLLBACK
cdcrollback.kafka.consumerGroup=cdcRollback
cdcrollback.kafka.clientId=cdc-rollback-producer

recon.kafka.bootstrapServers=10.4.38.95:9092,10.4.33.76:9092,10.4.33.103:9092
recon.kafka.topicName=recents_data
recon.kafka.consumerGroup=reconNew

#Kafka Properties
recent.dwh.kafka.bootstrapServers=10.4.38.95:9092,10.4.33.76:9092,10.4.33.103:9092
recent.dwh.kafka.topicName=recents_data
recent.dwh.kafka.consumerGroup=recharge_saga
recent.dwh.kafka.clientId=recent-data-dwh-producer

#Kafka Properties
validation.kafka.bootstrapServers=10.4.33.90:9092,10.4.33.198:9092,10.4.33.158:9092
validation.kafka.topicName_DEFAULT_VALIDATION=DEFAULT_VALIDATION
validation.kafka.topicName_RECHARGE_VALIDATION=RECHARGE_VALIDATION
validation.kafka.topicName_UTILITY_ELECTRICITY_VALIDATION=UTILITY_ELECTRICITY_VALIDATION
validation.kafka.topicName_BFSI_INSURANCE_VALIDATION=BFSI_INSURANCE_VALIDATION
validation.kafka.consumerGroup=recharges_saga_consumer

#Upi Credit Card Kafka Properties
upi.cc.kafka.bootstrapServers=tpap-privatelink-clients.paytm.com:9093,tpap-privatelink-clients.paytm.com:9094,tpap-privatelink-clients.paytm.com:9095
upi.cc.kafka.sshEnabled=true
upi.cc.kafka.topicName=upi_creditcard_events
upi.cc.kafka.consumerGroup=saga_upi_cc
upi.cc.kafka.maxPollInterval=240000
upi.cc.kafka.sessionTimeout=60000

upi.cc.bankNameMapping={"AU SMALL FINANCE BANK CREDIT CARD" : "AUBL","BANK OF BARODA CREDIT CARDS" : "BOB","CITY UNION BANK LIMITED CREDIT CARD" : "CITIUB",\
  "Canara Bank Credit Card" : "CANARA","CANARA BANK CREDIT CARD" : "CANARA","CSB BANK LIMITED CBL" : "CSB","FEDERAL BANK CREDIT CARD FBC" : "FDEB",\
  "HDFC BANK CREDIT CARD" : "HDFC","HDFC BANK NEW MOBILE" : "HDFC","ICICI BANK CREDIT CARD" : "ICICI","IDFC BANK CREDIT CARD" : "IDFC",\
  "INDIAN BANK CREDIT CARD" : "INDB","INDUSIND BANK CREDIT CARD" : "INDS","Kotak Mahindra Bank Credit Card" : "NKMB","Punjab National Bank Credit Card" : "PNB",\
  "RBL BANK LIMITED CREDIT CARD" : "RATN","State Bank of India PSP" : "SBI","SBI Credit Card" : "SBI","SARASWAT CO OPERATIVE BANK CREDIT CARD" : "STB",\
  "SBM BANK INDIA LIMITED SBA" : "SBMI","UNION BANK CREDIT CARD" : "UNI","Axis Bank Credit Card" : "AXIS","AXIS BANK CREDIT CARD" : "AXIS"\
  ,"YES BANK CREDIT CARD" : "YES"}

upi.cc.ifscCodeMapping={"AUBL00CCUPI":"AUBL","BARB0RCCUPI":"BOB","CIUB0CMS001":"CITIUB","CNRB0001912":"CANARA","CNRB00CCUPI":"CANARA","CSBK0CC0001":"CSB","FDRL00CCUPI":"FDEB","HDCC":"HDFC","HDNM":"HDFC","ICIC0DC0095":"ICICI","IDCC0040101":"IDFC","IDIB000C106":"INDB","INDB0CC0001":"INDS","KKBK00UPICC":"NKMB","PUNB0645400":"PNB","RATN0CRDXXX":"RATN","SBIN0112233":"SBI","SBIN0CONUPI":"SBI","SRCB00CCUPI":"STB","STCB0000011":"SBMI","UBIN0907826":"UNI","UTIB0000400":"AXIS","UTIB0AXISCC":"AXIS","YESB0UPIRCC":"YES"}

fsrecharge.kafka.bootstrapServers=10.4.33.90:9092,10.4.33.198:9092,10.4.33.158:9092
fsrecharge.kafka.topicName=RECHARGE_ORDER,EDUCATION_ORDER,BFSI_INSURANCE_ORDER,BFSI_METRO_ORDER,UTILITY_ELECTRICITY_ORDER,BFSI_GOLD_ORDER,BFSI_TOLL_ORDER,BFSI_GOOGLEPLAY_ORDER,UTILITY_CHALLAN_ORDER,DONATION_ORDER,TAPTOPAY_ORDER,DEFAULT_ORDER
fsrecharge.kafka.consumerGroup=abc

#check with devops and update bootstrap servers
upi.cc.bulk.kafka.bootstrapServers=127.0.0.1:9092
upi.cc.bulk.kafka.topicName=bulk_upi_creditcard_events
upi.cc.bulk.kafka.consumerGroup=saga_upi_cc_bulk
#PG propperties
pg.secretKey=V8OYIkXGG5cY/eYuUD4/xlDr0Sc4g4svvGN9QsQ4

#RestClientConfig
http.pg.maxConnectionPerRoute=2000
http.pg.connectTimeout=500
http.pg.readTimeout=1000
http.pg.requestTimeout=1500

http.recent.maxConnectionPerRoute: 500
http.recent.connectTimeout: 500
http.recent.readTimeout: 400
http.recent.requestTimeout: 700

http.reminder.maxConnectionPerRoute: 500
http.reminder.connectTimeout: 500
http.reminder.readTimeout: 400
http.reminder.requestTimeout: 700

http.smscards.maxConnectionPerRoute=2000
http.smscards.connectTimeout=300
http.smscards.readTimeout=500
http.smscards.requestTimeout=700

http.dcat.maxConnectionPerRoute: 1000
http.dcat.connectTimeout: 500
http.dcat.readTimeout: 500
http.dcat.requestTimeout: 1000

http.generic.maxConnectionPerRoute=1000
http.generic.connectTimeout=5000
http.generic.readTimeout=10000
http.generic.requestTimeout=10000

http.rps.maxConnectionPerRoute= 1000
http.rps.connectTimeout: 500
http.rps.readTimeout: 10000
http.rps.requestTimeout: 10000

http.oms.maxConnectionPerRoute: 10
http.oms.connectTimeout: 5000
http.oms.readTimeout: 10000
http.oms.requestTimeout: 10000

http.es.maxConnectionPerRoute: 10
http.es.connectTimeout: 5000
http.es.readTimeout: 10000
http.es.requestTimeout: 10000

http.insurance.maxConnectionPerRoute: 100
http.insurance.connectTimeout: 1000
http.insurance.readTimeout: 4000
http.insurance.requestTimeout: 1000


#Tomcat threads
server.tomcat.max-threads=400

automatic.data.ttl.days=7
utility.header.suggested.recharge.text=Proceed to do an instant payment on Paytm
utility.header.operatorChange.text=We detected an operator change from %s %s to %s %s

ruleEngine.host=http://digitalruleengine-internal.prod.paytmdgt.io
ruleEngine.endpoints.offloadcpuData=/v1/offloadcpu/data

#Smart Reminder
smartReminder.billDueDate.within.dayNum=5
recents.size.agent.threshhold=15

ruleEngineActiveInactive.host=http://digitalruleengine-internal.prod.paytmdgt.io

#Localisation
localisation.service=FAVOURITE
localisation.language=all
localisation.environment=production-mb
localisation.reloadInterval=20

recent.includeOperator.key.services=dth,donation,devotion,subscriptions

http.evictcache.url=http://smartreminderrechargebff.paytmdgt.io/evictcache
http.evictcache.upsertUrl=http://ffrechargesbff.paytmdgt.io/upsertOrderCounts
http.evictcache.retryLimit=2
http.evictcache.retryInterval=1000

#Peppipost API
peppipostApi.url=https://gptmtrans.pepipost.com/v5/mail/send
peppipostApi.key=********************************

#Recent Recon Mail config
recentRecon.mail.from.email=<EMAIL>
recentRecon.mail.from.name=BBPS Dev
recentRecon.mail.replyTo=<EMAIL>
#recentRecon.mail.to.names=Shivansh Kumar,Pankaj Palni,Gurjinder Singh
#recentRecon.mail.to.emails=<EMAIL>,<EMAIL>,<EMAIL>
recentRecon.mail.to={'<EMAIL>':'Shivansh Kumar','<EMAIL>':'Pankaj Palni','<EMAIL>':'Gurjinder Singh','<EMAIL>':'IN-Techops'}

#vault config


saga.cloud.vault.authentication=approle
saga.cloud.vault.uri=https://vault.paytmmall.io
saga.cloud.vault.app-role.role-id=13725650-06de-825f-9d70-84cee2f2532f
saga.cloud.vault.app-role.secret-id=7345c531-56ef-98a4-617a-aa6d1886cecc
saga.cloud.vault.kv.enabled=true
saga.cloud.vault.kv.backend=kv
saga.cloud.vault.application-name=kv/digital/recharges/recharge-saga/secrets
saga.cloud.vault.kv.default-context=kv/digital/recharges/recharge-saga/secrets
saga.cloud.vault.generic.enabled=false
saga.cloud.vault.fail-fast=true


#Thread Pool Task Executors
frequent-order.dropOff.config.http.minPoolSize=200
frequent-order.dropOff.config.http.maxPoolSize=400
frequent-order.dropOff.config.http.queueSize=25
frequent-order.dropOff.config.http.keepAlive=60

frequent-order.recent.config.http.minPoolSize=200
frequent-order.recent.config.http.maxPoolSize=400
frequent-order.recent.config.http.queueSize=25
frequent-order.recent.config.http.keepAlive=60

frequent-order.smart-recent.config.http.minPoolSize=200
frequent-order.smart-recent.config.http.maxPoolSize=400
frequent-order.smart-recent.config.http.queueSize=25
frequent-order.smart-recent.config.http.keepAlive=60

frequent-order.cc.reco.config.http.minPoolSize=200
frequent-order.cc.reco.config.http.maxPoolSize=400
frequent-order.cc.reco.config.http.queueSize=25
frequent-order.cc.reco.config.http.keepAlive=60

#removePgToken Kafka Properties
remove-pg-token.kafka.bootstrapServers=10.4.33.39:9092,10.4.33.171:9092,10.4.33.148:9092
remove-pg-token.kafka.topicName=PG_DELETED_AMW
remove-pg-token.kafka.consumerGroup=recharges_saga_consumer

fastag.dns= https://mmv-service.paytm.com
fastag.vehicledetails= api/v1/mmv/fetchVehicleDetails
fastag.passcode= d124bda2-b712-4f4e-ad10-a2711dd0f428
fastag.frequentOrderWaitTime=1000
fastag.maxParallelHits=2
fastag.minPoolSize= 100
fastag.maxPoolSize= 500
fastag.queueSize= 1000
fastag.keepAlive= 60

timeout.reco.cc=1


