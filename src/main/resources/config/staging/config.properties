application.environment = staging

#Cassandra Properties
cassandra.keyspace-name.saga=demo
    cassandra.keyspace-name.recent=recent
cassandra.contact-points=10.4.44.83
cassandra.port=9042
cassandra.username=user_saga
cassandra.dc.saga=dc1
cassandra.dc.recent=dc1

#JPA Properties for MYSQL
spring.datasource.url=***********************************
spring.datasource.username=app_recharge
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver


external.endpoints.reminderMarkAsPaid=https://digitalproxy-staging.paytm.com/bills/v1/markAsPaid
external.endpoints.recentsCRUDUpdate=http://digitalfavourite-staging.paytmdgt.io/v1/recentupdate/admin
external.endpoints.browsePlanUrl=https://digitalcatalog-staging.paytm.com/dcat/v1/browseplans/
dcat.categoryUrl=https://digitalcatalog-staging.paytm.com/dcat/v1/category/
#external.endpoints.pgSavedCards=https://securegw-stage.paytm.in/savedcardservice/savedcardOpenAPIService/v1/savedcardsByTokenType
external.endpoints.pgSavedCards=https://pgp-qa5.paytm.in/savedcardservice/savedcardOpenAPIService/v1/savedcardsByUserId
external.endpoints.smsCards=http://remindernode.nonprod.onus.paytmdgt.io/v2/bill-nonPaytm/getBill
external.endpoints.plansUrl=https://digitalcatalog-staging.paytm.com/rps/v1/plans/
external.endpoints.itemV2Url=http://oms-staging.paytm.com/v2/admin/items_v2.json
#external.endpoints.itemV2Url=http://order-internal.paytm.com/v2/admin/items_v2.json
external.endpoints.elasticSearch=http://pocrechargees.nonprod.onus.paytmdgt.io:9200/recharge_mis/_search
external.endpoints.billsSyncMarkAsPaid=http://billsyncspringboot.nonprod.onus.paytmdgt.io/dbs/v1/markaspaid
#Kafka Properties
oms.kafka.bootstrapServers=order-staging-kafka-v35.paytm.internal:9092
oms.kafka.topicName=order_update
oms.kafka.consumerGroup=new112311

#Kafka Properties
scratch.card.kafka.bootstrapServers=chat-nonprod-kafka.paytmdgt.io:9092
scratch.card.kafka.topicName=scratchcards_1
scratch.card.kafka.consumerGroup=promo1122211212

#Kafka Properties
plan.validity.kafka.bootstrapServers=10.4.41.200:9092
plan.validity.kafka.topicName=PLAN_VALIDITY_MAXWELL
plan.validity.kafka.consumerGroup=abc1

#Kafka Properties
validation.kafka.bootstrapServers=10.4.41.200:9092
validation.kafka.topicName_DEFAULT_VALIDATION=DEFAULT_VALIDATION
validation.kafka.topicName_RECHARGE_VALIDATION=RECHARGE_VALIDATION
validation.kafka.topicName_BFSI_INSURANCE_VALIDATION=BFSI_INSURANCE_VALIDATION
validation.kafka.topicName_UTILITY_ELECTRICITY_VALIDATION=UTILITY_ELECTRICITY_VALIDATION
validation.kafka.consumerGroup=abcjhbhgvhgvg

#Kafka Properties
reminder.kafka.bootstrapServers=10.4.41.200:9092
reminder.kafka.topicName=REMINDER_MAXWELL
reminder.kafka.consumerGroup=abc

automatic.kafka.bootstrapServers=10.4.41.200:9092
automatic.kafka.topicName=AUTOMATIC_SUBS_DATA
automatic.kafka.consumerGroup=abc

#billerAccount Kafka Properties
billeraccount.kafka.bootstrapServers=10.4.41.200:9092
billeraccount.kafka.topicNames=BILLER_ACCOUNT_DATA,BILLER_ACCOUNT_ADDITION_DATA
billeraccount.kafka.consumerGroup=abc

#Kafka Properties
evictcache.kafka.bootstrapServers=10.4.41.200:9092
evictcache.kafka.topicName=EVICT_CACHE
evictcache.kafka.consumerGroup=abc
evictcache.kafka.clientId=evict_cache_producer

http.evictcache.url=http://recharges-bffspringboot.nonprod.onus.paytmdgt.io/evictcache
http.evictcache.upsertUrl=http://recharges-bffspringboot.nonprod.onus.paytmdgt.io/upsertOrderCounts
http.evictcache.retryLimit=2
http.evictcache.retryInterval=2000


recon.kafka.bootstrapServers=10.4.41.200:9092
recon.kafka.topicName=recents_data
recon.kafka.consumerGroup=recon

#Kafka Properties
recent.dwh.kafka.bootstrapServers=10.4.41.200:9092
recent.dwh.kafka.topicName=recents_data
recent.dwh.kafka.consumerGroup=recharge_saga
recent.dwh.kafka.clientId=recent-data-dwh-producer


cdcreminder.kafka.bootstrapServers=10.4.41.200:9092
cdcreminder.kafka.topicName_SMS=recon.reminder.bills_non_paytm
cdcreminder.kafka.topicNameSmsRecovery=CDC_RECOVERY
cdcreminder.kafka.topicName_P2P=recon.reminder.bills_nonru
cdcreminder.kafka.consumerGroup=cdc

cdcrollback.kafka.bootstrapServers=10.4.41.200:9092
cdcrollback.kafka.topicName=CDC_ROLLBACK
cdcrollback.kafka.consumerGroup=cdcRollback
cdcrollback.kafka.clientId=cdc-rollback-producer

fsrecharge.kafka.bootstrapServers=10.4.41.200:9092
fsrecharge.kafka.topicName=RECHARGE_ORDER,EDUCATION_ORDER,BFSI_INSURANCE_ORDER,BFSI_METRO_ORDER,UTILITY_ELECTRICITY_ORDER,BFSI_GOLD_ORDER,BFSI_TOLL_ORDER,BFSI_GOOGLEPLAY_ORDER,UTILITY_CHALLAN_ORDER,DONATION_ORDER,TAPTOPAY_ORDER,DEFAULT_ORDER
fsrecharge.kafka.consumerGroup=abc

#PREPAID_BILLS Kafka Properties
prepaid.bills.kafka.bootstrapServers=10.4.41.200:9092
prepaid.bills.kafka.topicName=PREPAID_BILLS_MAXWELL
prepaid.bills.kafka.consumerGroup=saga_prepaid_bills

#Upi Credit Card Kafka Properties
upi.cc.kafka.sshEnabled=false
upi.cc.kafka.bootstrapServers=10.4.41.200:9092
upi.cc.kafka.topicName=upi_creditcard_events
upi.cc.kafka.consumerGroup=saga_upi_cc
upi.cc.kafka.maxPollInterval=120000
upi.cc.kafka.sessionTimeout=30000

upi.cc.bankNameMapping={"AU Small Finance Bank Rupay Credit Card" : "AUBL","Bank of Baroda Rupay Credit Card" : "BOB","CITY UNION BANK LIMITED CREDIT CARD" : "CITIUB","Canara Bank Credit Card" : "CANARA","Canara Bank Rupay Credit Card" : "CANARA",\
  "CSB Bank Rupay Credit Card" : "CSB","Federal Bank Rupay Credit Card" : "FDEB","HDFC Bank Rupay Credit Card" : "HDFC","HDFC BANK NEW MOBILE" : "HDFC","ICICI Bank Rupay Credit Card" : "ICICI","IDFC FIRST Rupay Credit Card" : "IDFC",\
  "Indian Bank Rupay Credit Card" : "INDB","Indusind Bank Rupay Credit Card" : "INDS","Kotak Mahindra Bank Rupay Credit Card" : "NKMB","Punjab National Bank Rupay Credit Card" : "PNB","RBL Bank RuPay Credit Card" : "RATN",\
  "State Bank of India PSP" : "SBI","SBI Rupay Credit Card" : "SBI","Saraswat Co-op Bank RuPay Credit Card" : "STB","SBM BANK INDIA RuPay Credit Card" : "SBMI","Union Bank Rupay Credit Card" : "UNI","Axis Bank Credit Card" : "AXIS",\
  "Axis Bank Rupay Credit Card" : "AXIS","Yes Bank Rupay Credit Card" : "YES"}

upi.cc.ifscCodeMapping={"AUBL00CCUPI":"AUBL","BARB0RCCUPI":"BOB","CIUB0CMS001":"CITIUB","CNRB0001912":"CANARA","CNRB00CCUPI":"CANARA","CSBK0CC0001":"CSB","FDRL00CCUPI":"FDEB","HDCC":"HDFC","HDNM":"HDFC","ICIC0DC0095":"ICICI","IDCC0040101":"IDFC","IDIB000C106":"INDB","INDB0CC0001":"INDS","KKBK00UPICC":"NKMB","PUNB0645400":"PNB","RATN0CRDXXX":"RATN","SBIN0112233":"SBI","SBIN0CONUPI":"SBI","SRCB00CCUPI":"STB","STCB0000011":"SBMI","UBIN0907826":"UNI","UTIB0000400":"AXIS","UTIB0AXISCC":"AXIS","YESB0UPIRCC":"YES"}

#PG propperties
pg.secretKey=V8OYIkXGG5cY/eYuUD4/xlDr0Sc4g4svvGN9QsQ4

#RestClientConfig
http.pg.maxConnectionPerRoute: 10
http.pg.connectTimeout: 5000
http.pg.readTimeout: 10000
http.pg.requestTimeout: 10000

http.oms.maxConnectionPerRoute: 10
http.oms.connectTimeout: 5000
http.oms.readTimeout: 10000
http.oms.requestTimeout: 10000

http.es.maxConnectionPerRoute: 10
http.es.connectTimeout: 5000
http.es.readTimeout: 10000
http.es.requestTimeout: 10000

http.recent.maxConnectionPerRoute: 10
http.recent.connectTimeout: 5000
http.recent.readTimeout: 10000
http.recent.requestTimeout: 10000

http.reminder.maxConnectionPerRoute: 10
http.reminder.connectTimeout: 6000
http.reminder.readTimeout: 9000
http.reminder.requestTimeout: 10000

http.smscards.maxConnectionPerRoute: 10
http.smscards.connectTimeout: 6000
http.smscards.readTimeout: 9000
http.smscards.requestTimeout: 10000

http.dcat.maxConnectionPerRoute: 10
http.dcat.connectTimeout: 5000
http.dcat.readTimeout: 10000
http.dcat.requestTimeout: 10000

http.generic.maxConnectionPerRoute: 1000
http.generic.connectTimeout: 5000
http.generic.readTimeout: 10000
http.generic.requestTimeout: 10000

http.rps.maxConnectionPerRoute: 10
http.rps.connectTimeout: 5000
http.rps.readTimeout: 10000
http.rps.requestTimeout: 10000

http.insurance.maxConnectionPerRoute: 100
http.insurance.connectTimeout: 5000
http.insurance.readTimeout: 2000
http.insurance.requestTimeout: 3000

automatic.data.ttl.days=30

utility.header.suggested.recharge.text=Proceed to do an instant payment on Paytm
utility.header.operatorChange.text=We detected an operator change from %s %s to %s %s

ruleEngine.host=https://digitalproxy-staging.paytm.com/digitalrecharge
ruleEngine.endpoints.offloadcpuData=/v1/offloadcpu/data

#Smart Reminder
smartReminder.billDueDate.within.dayNum=5
recents.size.agent.threshhold=100

ruleEngineActiveInactive.host=https://digitalproxy-staging.paytm.com/digitaloffloading

#Localisation
localisation.service=FAVOURITE
localisation.language=all
localisation.environment=staging
localisation.reloadInterval=20

recent.includeOperator.key.services=dth,donation,devotion

#Peppipost API
peppipostApi.url=https://gptmtrans.pepipost.com/v5/mail/send
peppipostApi.key=********************************

#Recent Recon Mail config
recentRecon.mail.from.email=<EMAIL>
recentRecon.mail.from.name=BBPS Dev
recentRecon.mail.replyTo=<EMAIL>

#Vault config

saga.cloud.vault.authentication=approle
saga.cloud.vault.uri=https://vault-staging.paytmmall.io
saga.cloud.vault.app-role.role-id=5c1fdea6-07bd-cedd-16b0-b80cfc187af5
saga.cloud.vault.app-role.secret-id=4714267e-9c03-2da4-5cd2-5bac87117412
saga.cloud.vault.kv.enabled=true
saga.cloud.vault.kv.backend=kv
saga.cloud.vault.application-name=kv/digital/recharges/recharge-saga/secrets
saga.cloud.vault.kv.default-context=kv/digital/recharges/recharge-saga/secrets
saga.cloud.vault.generic.enabled=false
saga.cloud.vault.fail-fast=true

#Thread Pool Task Executors
frequent-order.dropOff.config.http.minPoolSize=20
frequent-order.dropOff.config.http.maxPoolSize=40
frequent-order.dropOff.config.http.queueSize=10
frequent-order.dropOff.config.http.keepAlive=60

frequent-order.recent.config.http.minPoolSize=20
frequent-order.recent.config.http.maxPoolSize=40
frequent-order.recent.config.http.queueSize=10
frequent-order.recent.config.http.keepAlive=60

frequent-order.smart-recent.config.http.minPoolSize=20
frequent-order.smart-recent.config.http.maxPoolSize=40
frequent-order.smart-recent.config.http.queueSize=10
frequent-order.smart-recent.config.http.keepAlive=60

frequent-order.cc.reco.config.http.minPoolSize=10
frequent-order.cc.reco.config.http.maxPoolSize=20
frequent-order.cc.reco.config.http.queueSize=10
frequent-order.cc.reco.config.http.keepAlive=60

#removePgToken Kafka Properties
remove-pg-token.kafka.bootstrapServers=10.4.41.200:9092
remove-pg-token.kafka.topicName=PG_DELETED_AMW
remove-pg-token.kafka.consumerGroup=abc

fastag.dns= http://mmv-service-staging.paytm.com
fastag.vehicledetails= api/v1/mmv/fetchVehicleDetails
fastag.passcode= e7ed4f36a8694d11b831123d69bf4a0e
fastag.frequentOrderWaitTime=1000
fastag.maxParallelHits=2
fastag.minPoolSize= 10
fastag.maxPoolSize= 20
fastag.queueSize= 500
fastag.keepAlive= 60

timeout.reco.cc=1