package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.DropOffBillsObject;
import com.paytm.saga.dto.DropOffResponse;
import com.paytm.saga.dto.FrequentOrderRequest;
import com.paytm.saga.dto.RecentConfig;
import com.paytm.saga.dto.catalogue.ActiveInactivePidMapCache;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.enums.PayType;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

import static com.paytm.saga.common.constant.Constants.CommonConstants.PARTIAL_DUE;
import static com.paytm.saga.common.constant.Constants.CommonConstants.TOTAL_DUE;

public class DropOffUtils {

    private static final Logger logger = LogManager.getLogger(DropOffUtils.class);


    private DropOffUtils() {

    }

    public static Date getBillDueOrExpiryDate(DropOffResponse dropOff) {
        if (!CollectionUtils.isEmpty(dropOff.getBills())) {
            DropOffBillsObject dropOffBillsObject = dropOff.getBills().get(0);
            if (dropOffBillsObject.getDue_date() != null) {
                return DateUtil.stringToDate(dropOffBillsObject.getDue_date(), DateFormats.DATE_TIME_FORMAT_2);

            }
            if (dropOffBillsObject.getExpiry() != null) {
                return DateUtil.stringToDate(dropOffBillsObject.getExpiry(), DateFormats.DATE_TIME_FORMAT_2);

            }
        }
        return null;
    }

    public static boolean isDropOffWithinRange(DropOffResponse dropOff, RecentConfig localisationConfig) {
        Date date = getBillDueOrExpiryDate(dropOff);
        Date currentDate = new Date();
        Date startDateRange = DateUtil.dateIncrDecr(currentDate, -localisationConfig.getBillsVisibilityDaysBefore());
        Date endDateRange = DateUtil.dateIncrDecr(currentDate, localisationConfig.getBillsVisibilityDaysAfter());
        return date != null && DateUtil.compareDateWithoutTime(date, startDateRange) > 0 && DateUtil.compareDateWithoutTime(date, endDateRange) < 0;
    }

    public static Double getAmount(DropOffResponse dropOff) {
        if (!CollectionUtils.isEmpty(dropOff.getBills())) {
            List<DropOffBillsObject> bills = dropOff.getBills();
            return bills.get(0).getAmount();
        }
        return null;
    }

    public static Long getOrderId(DropOffResponse dropOff) {
        if (!CollectionUtils.isEmpty(dropOff.getBills())) {
            return dropOff.getBills().get(0).getOrderId();
        }
        return null;
    }

    public static Map<String, String> getAddionalInfo(DropOffResponse dropOff) {
        Map<String, String> additionalInfo = new HashMap<>();

        if (!CollectionUtils.isEmpty(dropOff.getBills())) {
            DropOffBillsObject bill = dropOff.getBills().get(0);
            additionalInfo.put(Constants.CommonConstants.PAN_UNIQUE_REFERENCE, bill.getPanUniqueReference());
            additionalInfo.put(Constants.CommonConstants.BILLS_OBJ_CIN, bill.getCin());
            additionalInfo.put(Constants.CommonConstants.TIN, bill.getTin());
        }
        return additionalInfo;
    }

    public static Map<String, String> getOperatorData(DropOffResponse dropOff) {

        Map<String, String> operatorData = new HashMap<>();
        if (PayType.CREDIT_CARD.value.equalsIgnoreCase(dropOff.getPaytype()) && !CollectionUtils.isEmpty(dropOff.getBills())) {
            DropOffBillsObject bill = dropOff.getBills().get(0);
            if (!StringUtils.isEmpty(bill.getPanUniqueReference())) {
                operatorData.put(Constants.CREDIT_CARD.PAN_UNIQUE_REFERENCE, bill.getPanUniqueReference());
                operatorData.put(Constants.CREDIT_CARD.TIN, bill.getTin());
            } else {
                operatorData.put(Constants.CREDIT_CARD.CREDIT_CARD_ID, bill.getCin());
            }
        }
        return operatorData;
    }

    private static String getUniqueKey(DropOffResponse dropOffResponse, RecentConfig config) {
        StringBuilder key = new StringBuilder("");
        ProductMin product = CVRProductCache.getInstance().getProductDetails(Long.valueOf(dropOffResponse.getProduct_id()));

        if (PayType.CREDIT_CARD.value.equalsIgnoreCase(product.getPayType())) {
            String creditCardOperator = CVRProductCache.getInstance().getCustomOperatorForCreditCard(Long.valueOf(dropOffResponse.getProduct_id()));
            key.append(dropOffResponse.getRecharge_number()).append(Constants.Delimiters.UNDERSCORE).append(dropOffResponse.getService()).append(Constants.Delimiters.UNDERSCORE).append(creditCardOperator);
        } else if (config.getIncludeOperatorInKeyServices().contains(product.getService())) {
            key.append(dropOffResponse.getRecharge_number()).append(Constants.Delimiters.UNDERSCORE).append(dropOffResponse.getService()).append(Constants.Delimiters.UNDERSCORE).append(product.getOperator());
        } else
            key.append(dropOffResponse.getRecharge_number()).append(Constants.Delimiters.UNDERSCORE).append(dropOffResponse.getService());


        return key.toString();
    }

    public static Map<String, DropOffResponse> getByRecentUniquenessLogic(Map<String, DropOffResponse> dropOffMap, RecentConfig config, FrequentOrderRequest request) {

        logger.info("DropOffUtils getByRecentUniquenessLogic starts ");

        Map<String, DropOffResponse> dropOffResponseMap = new HashMap<>();
        for (Map.Entry<String, DropOffResponse> dropOff : dropOffMap.entrySet()) {
            DropOffResponse response = dropOff.getValue();

            response.setProduct_id(ActiveInactivePidMapCache.getInstance().getActivePid(response.getProduct_id()));

            if (CVRProductCache.getInstance().isInactivePID(response.getProduct_id())
                    || (Objects.nonNull(request.getServices())
                        && !request.getServices().isEmpty()
                        && request.getServices().indexOf(response.getService())<0))
                continue;


            if (PayType.CREDIT_CARD.value.equalsIgnoreCase(response.getPaytype()))
                response.setRecharge_number(RecentUtils.maskMCN(response.getRecharge_number()));
            String key = getUniqueKey(response, config);


            if ((dropOffResponseMap.containsKey(key) && dropOffResponseMap.get(key).getTimestamp().compareTo(response.getTimestamp()) < 0)
                    || !dropOffResponseMap.containsKey(key))
                dropOffResponseMap.put(key, response);
        }

        return dropOffResponseMap;

    }


    public static boolean checkForClientVersion(JSONObject config, FrequentOrderRequest request, Long categoryId) {

        logger.info("inside checkForClientVersion");

        if (request.getClient() == null || request.getVersion() == null || config == null)
            return false;
        VersionComparable requestedVersion = new VersionComparable(request.getVersion());
        JSONArray categoryJson = null;
        if (config.has(request.getClient())) {
            JSONObject clientJson = (JSONObject) config.get(request.getClient());
            if (clientJson.has("rollOut") && clientJson.get("rollOut").toString().equalsIgnoreCase("COMPLETE"))
                return true;
            if (clientJson.has("versions")) {
                categoryJson = getCategoryJson(clientJson, requestedVersion);
                return checkIfCategoryExist(categoryJson, categoryId);


            }
        }
        return false;
    }

    private static boolean checkIfCategoryExist(JSONArray categoryJson, Long categoryId) {
        if (categoryJson != null) {

            for (Iterator<Object> it = categoryJson.iterator(); it.hasNext(); ) {
                String category = it.next().toString();
                if (category.equalsIgnoreCase(String.valueOf(categoryId)))
                    return true;


            }
        }

        return false;

    }

    private static JSONArray getCategoryJson(JSONObject clientJson, VersionComparable requestedVersion) {

        JSONObject versionJson = (JSONObject) clientJson.get("versions");
        Set<String> versions = versionJson.keySet();
        versions = versions.stream().sorted(Comparator.comparing(VersionComparable::new).reversed()).collect(Collectors.toCollection(LinkedHashSet::new));
        for (String version : versions) {
            if (requestedVersion.compareTo(new VersionComparable(version)) >= 0) {
                return (JSONArray) versionJson.get(version);

            }
        }
        return null;
    }

    public static boolean isTxnAutomatic(DropOffResponse dropOffResponse) {
        if (!CollectionUtils.isEmpty(dropOffResponse.getBills())) {
            if (Constants.ReminderConstants.AUTOMATIC_CHANNEL.equalsIgnoreCase(dropOffResponse.getBills().get(0).getChannel())) {
                return true;
            }
        }
        return false;
    }

    public static Boolean isBillDue(DropOffBillsObject dropOffBillsObject) {

        if (dropOffBillsObject.getDue_date() != null && dropOffBillsObject.getAmount() != null && dropOffBillsObject.getAmount() > 0)
            return Boolean.TRUE;
        return Boolean.FALSE;
    }

    public static String getBillState(DropOffBillsObject dropOffBillsObject, String payType) {

        if (!PayType.CREDIT_CARD.value.equalsIgnoreCase(payType) || (dropOffBillsObject.getReminder_amount() != null && dropOffBillsObject.getReminder_amount().equals(dropOffBillsObject.getOriginal_due_amount())))
            return TOTAL_DUE;

        return PARTIAL_DUE;
    }


}

