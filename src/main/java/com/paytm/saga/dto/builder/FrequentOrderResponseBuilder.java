package com.paytm.saga.dto.builder;


import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.RecentConstants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.enums.*;
import com.paytm.saga.enums.BillType;
import com.paytm.saga.model.Recents;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import java.util.*;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.*;
import static com.paytm.saga.common.constant.ServiceNameConstants.*;

public class FrequentOrderResponseBuilder {

    private final static Logger logger = LogManager.getLogger(FrequentOrderResponseBuilder.class);

    public static FrequentOrderResponse recentToFrequentOrderResponse(Recents recent, MetricsHelper metricsHelper, ServiceConfig serviceConfig) {

        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setChannel(recent.getChannelId());
        response.setConsumerName(recent.getConsumerName());
        response.setCylinderAgencyName(recent.getCylinderAgencyName());
        response.setNickName(recent.getNickName());
        response.setRentConsent(RENTCONSENT.valueOfInteger(recent.getRentConsent()).toString());
        response.setAutomaticState(recent.getAutomaticStatus());


        response.setAutomaticDate(DateUtil.formatDate(recent.getAutomaticDate(), DateFormats.DATE_TIME_FORMAT_2));
        response.setAutomaticAmount(recent.getAutomaticAmount());
        Map<String,Object> extraInfo = new HashMap<>();

        if(!StringUtils.isEmpty(recent.getExtra())){
            extraInfo = JsonUtils.parseJson(recent.getExtra(),Map.class);
        }
        if(Objects.nonNull(extraInfo)){
            if(extraInfo.containsKey(AMBIGUOUS_MULTI_PID_DEMERGER)){
                response.setAmbiguous(parseAmbiguous(extraInfo));
            }
            if(extraInfo.containsKey(Constants.IS_DEFAULT_AMOUNT)){
                response.setIsDefaultAmount((Boolean) extraInfo.get(IS_DEFAULT_AMOUNT));
            }
            if(serviceConfig.checkIfSubcriberConfigAllowed(recent.getKey().getService()) && extraInfo.containsKey(SUBSCRIBER_DETAILS)) {
                response.setSubscriberDetails(extraInfo.get(SUBSCRIBER_DETAILS));
                metricsHelper.pushToDD(SUBSCRIBER_DETAILS, recent.getKey().getService());
            }
            if (RecentUtils.checkIfPrepaidCase(recent)){
                response.setIsPrepaid(RecentUtils.checkIfPrepaidCase(recent));
            }else{
                response.setIsPrepaid(RecentUtils.checkIfPrepaidCase(recent) && !recent.getReminderStatus().equals(REMINDER_BILL_PAID_STATUS));
            }
            if (extraInfo.containsKey(IS_DWH_SMS_PARSING_MANUAL)) {
                response.setIsDwhSmsParsingManual(
					Boolean.TRUE.equals(extraInfo.get(IS_DWH_SMS_PARSING_MANUAL))
                );
            }
        }
        Boolean isGroupDisplayEnabled = null;
        Boolean isAmountEditable = null;
        Boolean isCAIdentifyCase = Boolean.FALSE;
        //this short-term fix is to handle different values received for isGroupDisplayEnabled and isAmountEditable where as expected value is true/value but received "0"/"1"
        if(Objects.nonNull(extraInfo)){
            try{
                Object groupDisplayEnabled=extraInfo.get("isGroupDisplayEnabled");
                if(Objects.nonNull(groupDisplayEnabled)){
                    if(groupDisplayEnabled instanceof  Boolean){
                        isGroupDisplayEnabled = (Boolean) groupDisplayEnabled;
                    }
                    else if (groupDisplayEnabled instanceof String) {
                        isGroupDisplayEnabled = "1".equals(groupDisplayEnabled) || Boolean.parseBoolean((String)groupDisplayEnabled);
                    }
                }
                Object amountEditable = extraInfo.get("isAmountEditable");
                if(Objects.nonNull(amountEditable)){
                    if (amountEditable instanceof Boolean) {
                        isAmountEditable = (Boolean) amountEditable;
                    }  else if (amountEditable instanceof String) {
                        isAmountEditable = "1".equals(amountEditable) || Boolean.parseBoolean((String)amountEditable);
                    }

                }
                Object updatedDataSource = extraInfo.get("updated_data_source");
                if(Objects.nonNull(updatedDataSource)){
                    if (updatedDataSource instanceof String) {
                        isCAIdentifyCase = ((String) updatedDataSource).endsWith("CA_IDENTIFY");
                    }

                }
                if(extraInfo.containsKey(Constants.IS_PAYTM_VPA)){
                    response.setIsPaytmVPA((Integer) extraInfo.get(Constants.IS_PAYTM_VPA));
                }

            }
            catch(Exception ex){
                logger.error("Exception while fetching isGroupDisplayEnabled and isGroupDisplayEnabled  ",ex);
            }

        }
        if(Objects.nonNull(isGroupDisplayEnabled))
            response.setGroupDisplayEnabled(isGroupDisplayEnabled);
        if(Objects.nonNull(isAmountEditable))
            response.setAmountEditable(isAmountEditable);
        if (recent.getNotificationStatus() != null) {
            response.setReminderNotificationEnabled(recent.getNotificationStatus().intValue() == 1);
        } else
            response.setReminderNotificationEnabled(Boolean.TRUE);
        response.setDate(DateUtil.formatDate(recent.getUpdatedAt(), DateFormats.DATE_TIME_FORMAT_2));
        response.setCreatedAt(DateUtil.formatDate(recent.getCreatedAt(), DateFormats.DATE_TIME_FORMAT_2));
        response.setPid(String.valueOf(recent.getProductId()));
        response.setEventType(EventType.RECENT);
        if(StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), Constants.EVENT_SOURCE.SMART_RECENT_DROPOFF,Constants.EVENT_SOURCE.SMART_RECENT_VALIDATION)){
            response.setEventType(EventType.SMART_RECENT);
        }
        else {
            response.setEventType(EventType.RECENT);
        }
        response.setOrderId(recent.getOrderId());
        response.setHasPaymentHistory(recent.getOrderId() != null);
        response.setEventState(EventStateHandler.getState(recent));
        response.setAmount(RecentUtils.getFrequentOrderResponseAmount(recent));
        if(response.getEventState().equals(EventState.RECHARGE_PENDING)
                || response.getEventState().equals(EventState.RECHARGE_FAILURE)
                || response.getEventState().equals(EventState.RECHARGE_FAILURE_CONSENT_PENDING)
                || response.getEventState().equals(EventState.RECHARGE_FAILURE_NO_CONSENT)
                || response.getEventState().equals(EventState.RECHARGE_PENDING_CONSENT_PENDING)
                || response.getEventState().equals(EventState.RECHARGE_PENDING_NO_CONSENT))
            response.setEventType(EventType.RECHARGE);
        if(response.getEventState().equals(EventState.NEW_ACCOUNT)){
            response.setEventType(EventType.VALIDATION);
        }
        if(Constants.EVENT_SOURCE.SMS.equals(recent.getEventSource()) && recent.getOrderId()==null){
            response.setEventType(EventType.SMS_CARD);
        }
        if(EVENT_SOURCE.UPI_CREDIT_CARD.equalsIgnoreCase(recent.getEventSource()) && recent.getOrderId()==null){
            response.setEventType(EventType.UPI_CARD);
        }
        if(Constants.EVENT_SOURCE.CSV.equalsIgnoreCase(recent.getEventSource()) && recent.getOrderId()==null){
            response.setEventType(EventType.CSV);
        }
        if(Constants.EVENT_SOURCE.PG_DELETED_AMW.equalsIgnoreCase(recent.getEventSource())){
            response.setEventType(EventType.PG_DELETED_AMW);
        }
        if(Constants.EVENT_SOURCE.RU_SMS.equalsIgnoreCase(recent.getEventSource()) && (Objects.nonNull(recent.getPgCardId()) && recent.getPgCardId().equals(recent.getKey().getRechargeNumber()))){
            response.setEventType(EventType.PG_DELETED_AMW);
        }
        if(response.getEventState().equals(EventState.NEW_ACCOUNT_BROWSE_PLAN)){
            response.setEventType(EventType.VALIDATION);
        }
        if (PayType.CREDIT_CARD.value.equalsIgnoreCase(recent.getPayType())) {
            response.setRechargeNumber1(recent.getMcn());
        } else response.setRechargeNumber1(recent.getKey().getRechargeNumber());


        response.setRechargeNumber2(recent.getRechargeNumber2());
        response.setRechargeNumber3(recent.getRechargeNumber3());
        response.setRechargeNumber4(recent.getRechargeNumber4());
        response.setRechargeNumber5(recent.getRechargeNumber5());
        response.setRechargeNumber6(recent.getRechargeNumber6());
        response.setRechargeNumber7(recent.getRechargeNumber7());
        response.setRechargeNumber8(recent.getRechargeNumber8());
        response.setAdditionalInfo(RecentUtils.getAddionalInfo(recent));
        response.getAdditionalInfo().put(RecentConstants.RECON_ID,recent.getReconId());
        response.setOperatorRecentData(RecentUtils.getRecentData(recent));
        response.setOperatorData(RecentUtils.getOperatorData(recent));
        response.getOperatorRecentData().putAll(response.getOperatorData());
        response.setRentTfData(RecentUtils.getRentTFDataFE(recent));
        response.setRemindLaterDate(DateUtil.formatDate(recent.getRemindLaterDate(),DateFormats.DATE_FORMAT_2));
        if(recent.getRemindLaterDate()!=null && DateUtil.compareDateWithoutTime(recent.getRemindLaterDate(),new Date())>=0) {
            response.setRemindLater(Boolean.TRUE);
        }
        response.setOprBillGenDate(DateUtil.formatDate(recent.getBillDate(), DateFormats.DATE_TIME_FORMAT_2));

        if(Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(recent.getKey().getService())) {
            response.setShowBBPSFlag(recent.getNextBillFetchDateFlag() == null || !recent.getNextBillFetchDateFlag());
            response.setConsentValidTill(DateUtil.formatDate(recent.getConsentValidTill(), DateFormats.DATE_TIME_FORMAT_2));
            if(recent.getConsentValidTill() != null && recent.getConsentValidTill().after(new Date())) {
                response.setHasConsent(1);
            } else {
                response.setHasConsent(0);
            }
        }


        if (!recent.isOnlyTxn()
            && (Boolean.TRUE.equals(RecentUtils.isBillDue(recent))
            || Boolean.TRUE.equals(RecentUtils.isBillDuePartial(recent))
                || RecentUtils.checkIfPrepaidCase(recent)
            || Boolean.TRUE.equals(RecentUtils.isBillDueRUPartial(recent, serviceConfig.getRUPartialBillRecoServices(), serviceConfig.getRuPartialBillUpdatedSourceList())))
            && !(Boolean.TRUE.equals(recent.getIsMarkAsPaid()) && RecentUtils.isMarkAsPaid(recent))
        )
        {
            BillObject bill = new BillObject();
            bill.setBillDate(DateUtil.formatDate(recent.getBillDate(), DateFormats.DATE_TIME_FORMAT_2));
            bill.setBillDueDate(DateUtil.formatDate(recent.getDueDate(), DateFormats.DATE_TIME_FORMAT_2));
            bill.setMarkAsPaidDate(DateUtil.formatDate(recent.getMarkAsPaidTime(), DateFormats.DATE_TIME_FORMAT_2));
            bill.setBillAmount(recent.getDueAmount());
            bill.setMin_due_amount(recent.getMinDueAmount());
            bill.setOriginal_due_amount(recent.getOriginalDueAmount());
            bill.setOriginal_min_due_amount(recent.getOriginalMinDueAmount());
            bill.setPlan_bucket(recent.getKey().getPlanBucket());
            bill.setMarkedAsPaid(RecentUtils.isMarkAsPaid(recent));
            bill.setIsBillDue((RecentUtils.isBillDue(recent) || RecentUtils.isBillDuePartial(recent) || RecentUtils.isBillDueRUPartial(recent, serviceConfig.getRUPartialBillRecoServices(), serviceConfig.getRuPartialBillUpdatedSourceList())));
            bill.setBillState(RecentUtils.getBillState(recent));
            bill.setPlan(RecentUtils.getPlanName(recent, recent.getPayType()));
            if(Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(recent.getKey().getService()) && Objects.nonNull(recent.getCurrentOutstandingAmount())) {
                bill.setCurrentOutstandingAmount(recent.getCurrentOutstandingAmount());
            }
            response.setBill(bill);
        }else{
            Date recentDueDate = null;
            if(RecentUtils.isMarkAsPaid(recent)){
                BillObject bill = new BillObject();
                bill.setMarkedAsPaid(RecentUtils.isMarkAsPaid(recent));
                response.setBill(bill);
            } else if(Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(recent.getKey().getService()) && Objects.nonNull(recent.getCurrentOutstandingAmount())) {
                BillObject bill = new BillObject();
                bill.setCurrentOutstandingAmount(recent.getCurrentOutstandingAmount());
                response.setBill(bill);
            }
            if(recent.getDueDate()!=null){
                String recentDueDateInString = DateUtil.formatDate(recent.getDueDate(), DateFormats.DATE_TIME_FORMAT_2);
                try {
                    recentDueDate = DateUtil.parseDate(recentDueDateInString, DateFormats.DATE_TIME_FORMAT_2);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }

            int comparisonResult =0;
            if(Objects.nonNull(recentDueDate)){
                comparisonResult = new Date().compareTo(recentDueDate);
            }

            if(recent.getDueDate()==null){
                response.setBillType(BillType.NO_BILL);
            }
            else if(comparisonResult > 0){ //recent due date is before the current date
                if(recent.getDueAmount()==null){
                    response.setBillType(BillType.FULL_BILL);
                }else{
                    if(recent.getDueAmount()<=0){
                        response.setBillType(BillType.FULL_BILL);
                    }else if(recent.getDueAmount()>0){
                        response.setBillType(BillType.NO_BILL);
                    }
                }
            }else{ //recent due date is greater than the current date
                response.setBillType(BillType.FULL_BILL);
            }
        }
        BillState billState = BillStateHandler.getState(recent);
        if (Constants.SERVICE_MOBILE.equalsIgnoreCase(recent.getKey().getService()) && Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType()) && billState != BillState.NO_DUE) {
            BillState partialBillState = BillStateHandler.getPartialBillState(recent);
            if (partialBillState != null) {
                billState = partialBillState;
            }
        }
        if(isCAIdentifyCase && Objects.isNull(recent.getDueDate()) && !RecentUtils.isMarkAsPaid(recent) && (Objects.isNull(recent.getDueAmount()) || recent.getDueAmount() <= 0.0) && (Objects.isNull(recent.getMinDueAmount()) || recent.getMinDueAmount() <= 0.0)){
            billState = BillState.NO_DUE;
        }

        if(RecentUtils.isBillDueRUPartial(recent, serviceConfig.getRUPartialBillRecoServices(), serviceConfig.getRuPartialBillUpdatedSourceList())) {
            billState = BillState.NO_DATE;
        }

        response.setBillState(billState);

        Map<String, Object> apiParams = new HashMap<>();
        if(recent.getCin() != null)
        {
            apiParams.put("cin", recent.getCin());
        }
        if(recent.getPar() != null)
        {
            apiParams.put("par", recent.getPar());
        }
        if(recent.getBbpsRefId() != null)
        {
            apiParams.put("bbpsRefId", recent.getBbpsRefId());
        }
        if(recent.getPgCardId() != null)
        {
            apiParams.put("pgCardId", recent.getPgCardId());
        }
        response.setApiParams(apiParams);
        if (recent.getNotPaidOnPaytm() != null && recent.getNotPaidOnPaytm().intValue() == 1){
            if(response.getBillState().equals(BillState.NO_DUE) || response.getBillState().equals(BillState.NO_DUE_OUT_AMT)){
                response.setNotPaidOnPaytm(Boolean.TRUE);
            }else{
                response.setNotPaidOnPaytm(Boolean.FALSE);
            }
        }
        response.setIsFullBill(!RecentUtils.isBillDuePartial(recent));
        response.setIsTxnAutomatic(RecentUtils.isTxnAutomatic(recent));
        response.setMarkAsPaidDate(DateUtil.formatDate(recent.getMarkAsPaidTime(), DateFormats.DATE_TIME_FORMAT_2));
        response.setTxnAmount(recent.getTxnAmount());
        response.setTxnDate(DateUtil.formatDate(recent.getTxnTime(), DateFormats.DATE_TIME_FORMAT_2));
        if(Service.RENT_PAYMENT.value.equalsIgnoreCase(recent.getKey().getService()) || Service.BUSINESS_PAYMENT.value.equalsIgnoreCase(recent.getKey().getService())){
            if(!StringUtils.isEmpty(recent.getRechargeNumber7()) && ! StringUtils.isEmpty(recent.getKey().getRechargeNumber()) && Objects.nonNull(recent.getIsNewBiller()) && recent.getIsNewBiller().equals(true)){
                response.setRechargeNumber1(recent.getRechargeNumber7());
                response.setRechargeNumber7(recent.getKey().getRechargeNumber());
            }
        } else if (Service.FASTAG.value.equalsIgnoreCase(recent.getKey().getService()) && !StringUtils.isEmpty(recent.getInsuranceCard())) {
            response.setFastagExtraData(parseInsuranceCardInfo(recent.getInsuranceCard(),recent));
        }

        response.setOriginalDueAmount(recent.getOriginalDueAmount());
        if(Objects.nonNull(recent.getEarlyPaymentDate())
                && Objects.nonNull(recent.getEarlyPaymentAmount())
                && recent.getEarlyPaymentAmount()>0
                && recent.getEarlyPaymentDate().after(new Date()))
            response.setEarlyPayment(true);
        RecentUtils.getFinalTxnDetails(response,recent);
        response.setAutomaticSubscriptionId(recent.getAutomaticSubscriptionId());

        OperatorValidationDto operatorValidationResponse = recent.getOperatorValidationDto();
        if(operatorValidationResponse!=null)
            response.setOperatorValidatedAt(operatorValidationResponse.getDate());


        if(Objects.nonNull(extraInfo)){
            Object updatedDataSource = extraInfo.get("updated_data_source");
            populateBillSource(updatedDataSource,recent,response,serviceConfig);

            if(extraInfo.containsKey(Constants.IS_DUMMY_RN) && extraInfo.get(Constants.IS_DUMMY_RN).equals("1")
                && extraInfo.containsKey(Constants.SMS_RN) && !StringUtils.isEmpty(extraInfo.get(Constants.SMS_RN).toString())){
                    response.setRechargeNumber1((String) extraInfo.get(Constants.SMS_RN));
            }
        }


        return response;

    }

    public static Map<String,Object> parseAmbiguous(Map<String,Object> extra){
        Map<String,Object> ambiguousJson=new HashMap<>();

        String ambiguousJsonString = null;
        Object ambiguousValue = extra.get(AMBIGUOUS_MULTI_PID_DEMERGER);

        if(ambiguousValue != null) {
            if (ambiguousValue instanceof Map) {
                ambiguousJson = (Map<String, Object>) ambiguousValue;
            } else {
                ambiguousJsonString = ambiguousValue.toString();
                if (!StringUtils.isEmpty(ambiguousJsonString)) {
                    try {
                        ambiguousJson = JsonUtils.parseJson(ambiguousJsonString, Map.class);
                    } catch (Exception e) {
                        logger.warn("Failed to parse 'ambiguous' JSON: {}", e.getMessage());
                    }
                }
            }
        }
        return ambiguousJson;
    }
    public static Map<String,Object> parseInsuranceCardInfo(String insuranceCardInfo, Recents recent){
        try{
            Map<String,Object> res=new HashMap<>();
            JSONObject extras = new JSONObject(insuranceCardInfo);
            if(Objects.nonNull(extras) && !extras.isEmpty() && extras.length()>0) {
                String[] keys = {"make", "model", "vehicleColor", "variantId"};
                for (int i = 0; i < keys.length; i++) {
                    Object value = null;
                    try {
                        value = extras.get(keys[i]);
                    } catch (Exception e) {
                    }
                    if(Objects.isNull(value) && !"vehicleColor".equalsIgnoreCase(keys[i])){
                        logger.warn("parseInsuranceCardInfo MMV data not found customer id {} recharge number {} operator {}",recent.getKey().getCustomerId(),recent.getKey().getRechargeNumber(),recent.getKey().getOperator());
                    }
                    res.put(keys[i], value);
                }
            }
            return res;
        }catch(Exception e){
            return null;
        }

    }

    private FrequentOrderResponseBuilder() {

    }

    public static FrequentOrderResponse build(DropOffResponse dropOff, Recents recent, boolean expiryWithinRange) {
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setChannel(dropOff.getChannel());
        response.setConsumerName(dropOff.getConsumerName());
        response.setDate(DateUtil.formatDate(dropOff.getTimestamp(), DateFormats.DATE_TIME_FORMAT_2));
        response.setPid(dropOff.getProduct_id());
        response.setAmount(DropOffUtils.getAmount(dropOff));
        response.setEventType(EventType.valueOf(dropOff.getType()));
        response.setOrderId(DropOffUtils.getOrderId(dropOff));
        response.setRechargeNumber1(dropOff.getRecharge_number());
        response.setRechargeNumber2(dropOff.getRecharge_number_2());
        response.setRechargeNumber3(dropOff.getRecharge_number_3());
        response.setRechargeNumber4(dropOff.getRecharge_number_4());
        response.setRechargeNumber5(dropOff.getRecharge_number_5());
        response.setRechargeNumber6(dropOff.getRecharge_number_6());
        response.setRechargeNumber7(dropOff.getRecharge_number_7());
        response.setRechargeNumber8(dropOff.getRecharge_number_8());


        Map<String, String> additionalInfo = DropOffUtils.getAddionalInfo(dropOff);
        if (Objects.nonNull(recent) && Objects.nonNull(recent.getIsNewBillIdentified())) {
            additionalInfo.put(IS_NEW_BILL_IDENTIFIED, String.valueOf(recent.getIsNewBillIdentified()));
        }
        response.setAdditionalInfo(additionalInfo);

        response.setOperatorRecentData(new HashMap<>());
        response.setRentTfData(new HashMap<>());

        if (recent != null) {
            Map<String,Object> extraInfo = new HashMap<>();

            if(!StringUtils.isEmpty(recent.getExtra())){
                extraInfo = JsonUtils.parseJson(recent.getExtra(),Map.class);
            }
            if(Objects.nonNull(extraInfo)){
                if(extraInfo.containsKey(Constants.IS_PAYTM_VPA)){
                    response.setIsPaytmVPA((Integer) extraInfo.get(Constants.IS_PAYTM_VPA));
                }
            }
            response.setAutomaticState(recent.getAutomaticStatus());
            response.setAutomaticDate(DateUtil.formatDate(recent.getAutomaticDate(), DateFormats.DATE_TIME_FORMAT_2));
            response.setNickName(recent.getNickName());
            response.setRentConsent(RENTCONSENT.valueOfInteger(recent.getRentConsent()).toString());
            response.setCylinderAgencyName(recent.getCylinderAgencyName());
            if (response.getConsumerName() == null)
                response.setConsumerName(recent.getConsumerName());
            response.setHasPaymentHistory(recent.getOrderId() != null);
            response.setMarkAsPaidDate(DateUtil.formatDate(recent.getMarkAsPaidTime(), DateFormats.DATE_TIME_FORMAT_2));
            response.setOperatorRecentData(RecentUtils.getRecentData(recent));
            if (!Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(recent.getKey().getService())) {
                response.setOperatorData(RecentUtils.getOperatorData(recent));
                response.getOperatorRecentData().putAll(response.getOperatorData());
            }
            else {
                response.setOperatorData(DropOffUtils.getOperatorData(dropOff));
                response.getOperatorRecentData().putAll(DropOffUtils.getOperatorData(dropOff));
            }
            response.getAdditionalInfo().putAll(RecentUtils.getDisplayValueMap(recent));
            response.getRentTfData().putAll(RecentUtils.getRentTFDataFE(recent));
        } else {
            response.setAutomaticState(Boolean.TRUE.equals(dropOff.getIsAutomaticActive()) ? 1 : 0);
            response.setOperatorData(DropOffUtils.getOperatorData(dropOff));
            response.getOperatorRecentData().putAll(DropOffUtils.getOperatorData(dropOff));
        }



        if (additionalInfo.get("cylinderAgencyName") != null) {
            response.setCylinderAgencyName(additionalInfo.get("cylinderAgencyName"));
        }

        if (expiryWithinRange) {
            BillObject bill = new BillObject();
            DropOffBillsObject dropOffBillsObject = dropOff.getBills().get(0);
            bill.setBillDate(dropOffBillsObject.getBill_date());
            bill.setBillDueDate(StringUtils.firstNonEmpty(dropOffBillsObject.getDue_date(), dropOffBillsObject.getExpiry()));
            bill.setBillAmount(dropOffBillsObject.getReminder_amount() != null ? dropOffBillsObject.getReminder_amount(): dropOffBillsObject.getAmount());
            bill.setMin_due_amount(dropOffBillsObject.getMin_due_amount());
            bill.setOriginal_due_amount(dropOffBillsObject.getOriginal_due_amount());
            bill.setOriginal_min_due_amount(dropOffBillsObject.getOriginal_min_due_amount());
            bill.setPlan_bucket(dropOffBillsObject.getPlan_bucket());
            bill.setIsBillDue(DropOffUtils.isBillDue(dropOffBillsObject));
            bill.setBillState(DropOffUtils.getBillState(dropOffBillsObject, dropOff.getPaytype()));

            if (recent != null) {
                bill.setMarkedAsPaid(RecentUtils.isMarkAsPaid(recent));
                bill.setMarkAsPaidDate(DateUtil.formatDate(recent.getMarkAsPaidTime(), DateFormats.DATE_TIME_FORMAT_2));
            } else {
                bill.setMarkedAsPaid(Boolean.FALSE);
            }
            bill.setPlan(RecentUtils.getPlanName(recent, dropOff.getPaytype()));
            response.setBill(bill);

            response.setBillState(BillStateHandler.getState(dropOff, RecentUtils.isAutomaticValid(recent)));
        } else {
            if(Objects.nonNull(recent) && Objects.nonNull(recent.getKey()) && FINANCIAL_SERVICES.equalsIgnoreCase(recent.getKey().getService()) && Objects.nonNull(recent.getCurrentOutstandingAmount()) && recent.getCurrentOutstandingAmount() > 0) {
                response.setBillState(BillState.NO_DUE_OUT_AMT);
            } else {
                response.setBillState(BillState.NO_DUE);
            }
        }


        response.setIsTxnAutomatic(DropOffUtils.isTxnAutomatic(dropOff));
        response.setTxnAmount(DropOffUtils.getAmount(dropOff));
        response.setTxnDate(DateUtil.formatDate(dropOff.getTimestamp(), DateFormats.DATE_TIME_FORMAT_2));
        response.setOperatorValidatedAt(dropOff.getTimestamp());
        response.setEventState(EventStateHandler.getState(dropOff));
        response.setReminderNotificationEnabled(true);
        return response;

    }


    private static void populateBillSource(Object updatedDataSource, Recents recent, FrequentOrderResponse response, ServiceConfig serviceConfig) {

        try {
            List<String> billsourcesList = serviceConfig.getSystemFetchBillSources();
            List<String> billSourcesServices = serviceConfig.getBillSourcesServices();
            List<String> smsBillSourcesList = serviceConfig.getSmsBillSources();
            ProductMin productMin = CVRProductCache.getInstance().getProductDetails(Long.valueOf(response.getPid()));
            if (Objects.isNull(productMin) || Objects.isNull(productMin.getService()) || Objects.isNull(billSourcesServices) || (!billSourcesServices.isEmpty() && !billSourcesServices.contains(productMin.getService().toLowerCase()))) {
                return;
            }

            if (Objects.nonNull(response.getBill()) && Boolean.TRUE.equals(response.getBill().getIsBillDue())) {
                if (Objects.isNull(billsourcesList) || billsourcesList.isEmpty()) {
                    response.getBill().setBill_source(null);
                    return;
                }

                if (Objects.nonNull(updatedDataSource)) {
                    if (updatedDataSource instanceof String) {
                        if (billsourcesList.contains(((String) updatedDataSource).toLowerCase())) {
                            response.getBill().setBill_source(BILL_SOURCE_SYSTEM_FETCH);
                        } else if (Objects.nonNull(smsBillSourcesList) && !smsBillSourcesList.isEmpty() && smsBillSourcesList.contains(((String) updatedDataSource).toLowerCase())) {
                            response.getBill().setBill_source(BILL_SOURCE_SMS_PARSING);
                        } else {
                            response.getBill().setBill_source(BILL_SOURCE_UNKNOWN);
                        }
                    }
                }
            }

        } catch (Exception ex) {
            logger.error("Exception occured :: populateBillSource", ex);
        }

    }

}
