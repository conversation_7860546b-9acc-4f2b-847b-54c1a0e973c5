package com.paytm.saga.repository;

import java.util.List;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.stereotype.Repository;

import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.DropOffPrimaryKey;


@Repository
public interface DropOffRepository extends CassandraRepository<DropOff, DropOffPrimaryKey>,CustomizedSave<DropOff> {



    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Query("select * from drop_off where customerId = ?0 limit 200") //changed
    public List<DropOff> findByCustomerId(Long customerId);

    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    public List<DropOff> findTop100ByCustomerIdInAndRechargeNumberAndService(List<Long> customerIds, String rechargeNumber , String service);

    //@Query("select * from drop_off where customerId = ?0 and service in (?1) limit 100")
   // public List<DropOff> findByCustomerIdAndServiceIn(Long customerId, List<String> service);

    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    public List<DropOff> findTop100ByCustomerIdAndRechargeNumberAndServiceIn(Long customerId, String rechargeNumber, List<String> service);

    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    public List<DropOff> findTop100ByCustomerIdAndRechargeNumberAndService(Long customerId, String rechargeNumber, String service);

}