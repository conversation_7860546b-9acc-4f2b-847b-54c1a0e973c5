package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.PrometheusConstants;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.RecentDao;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.builder.FrequentOrderResponseBuilder;
import com.paytm.saga.dto.catalogue.ActiveInactivePidMapCache;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.enums.BillState;
import com.paytm.saga.enums.EventType;
import com.paytm.saga.enums.PayType;
import com.paytm.saga.enums.Service;
import com.paytm.saga.model.Recents;
import com.paytm.saga.service.aggregator.pidmapmanager.ActiveInactivePidMap;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.DropOffUtils;
import com.paytm.saga.util.RecentUtils;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.paytm.saga.common.constant.Constants.EVENT_SOURCE.UPI_CREDIT_CARD;
import static com.paytm.saga.common.constant.Constants.UPDATED_SOURCE_KEY;
import static com.paytm.saga.common.constant.Constants.BILL_FETCH_DATE_TIME_KEY;
import static com.paytm.saga.common.constant.Constants.SMS_DATE_TIME_KEY;
import static com.paytm.saga.common.constant.Constants.PAYMENT_DATE_TIME_KEY;
import static com.paytm.saga.common.constant.Constants.TRANSACTION_SOURCE_KEY;
import static com.paytm.saga.common.constant.Constants.VALIDATION_SYNC_SOURCE_KEY;
import static com.paytm.saga.common.constant.Constants.FFR_SOURCE_KEY;
import static com.paytm.saga.common.constant.Constants.CommonConstants.*;
import static com.paytm.saga.common.constant.Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET;
import static com.paytm.saga.common.constant.Constants.MetricConstants.*;
import static com.paytm.saga.common.constant.ServiceNameConstants.IS_SMS_LIVE;
import static com.paytm.saga.dto.builder.FrequentOrderResponseBuilder.*;
import static com.paytm.saga.enums.Service.ELECTRICITY;


public abstract class FrequentOrderServiceImpl implements FrequentOrderService {

    private final CustomLogger logger = CustomLogManager.getLogger(FrequentOrderServiceImpl.class);

    @Autowired
    @Qualifier("monitoringClient")
    protected StatsDClient monitoringClient;

    @Autowired
    @Qualifier("frequentOrderRecentExecutor")
    protected ExecutorService frequentOrderRecentExecutor;

    @Autowired
    @Qualifier("frequentOrderDropOffExecutor")
    protected ExecutorService frequentOrderDropOffExecutor;

    @Autowired
    @Qualifier("frequentOrderSmartRecentExecutor")
    protected ExecutorService frequentOrderSmartRecentExecutor;

    @Autowired
    @Qualifier("frequentOrderCCRecoExecutor")
    protected ExecutorService frequentOrderCCRecoExecutor;

    @Autowired
    protected ServiceConfig serviceConfig;

    @Autowired
    protected RecentDao dao;
    @Autowired
    protected DropOffService dropOffService;
    @Autowired
    private UserAgentService userAgentService;
    @Value("#{'${recent.includeOperator.key.services}'.split(',')}")
    private List<String> includeOperatorInKeyServices;

    @Autowired
    private ActiveInactivePidMap activeInactivePidMap;

    @Autowired
    private LocalisationManager localisationManager;

    @Autowired
    protected SmartRecentsService smartRecentsService;

    @Autowired
    private RecentsService recentsService;

    @Autowired
    private MetricsHelper metricsHelper;


    protected FrequentOrderServiceImpl() {
    }


    @Override
    public List<FrequentOrderResponse> getFrequentOrders(FrequentOrderRequest request) throws RechargeSagaBaseException {

        FrequentOrderResponseWrapper wrapper = getFrequentOrderWrapper(request);
        return wrapper.getData();
    }


    @Override
    public FrequentOrderResponseWrapper getFrequentOrderWrapper(FrequentOrderRequest request) throws RechargeSagaBaseException {
        logger.info("getFrequentOrders starts");

        FrequentOrderResponseWrapper wrapper = new FrequentOrderResponseWrapper();

        if (Boolean.TRUE.equals(userAgentService.isAgent(request.getCustomerId()))) {
            wrapper.setAgent(true);
            return wrapper;
        }

        List<Recents> recents = new ArrayList<>();
        Map<String, DropOffResponse> dropOffMap = new HashMap<>();

        updateSortedRecentsAndDropoff(recents, dropOffMap, request, wrapper);
        logger.info("updateSortedRecentsAndDropoff ends, recents - {}, dropOffMap - {}", recents, dropOffMap);
        if (!CollectionUtils.isEmpty(recents) || !CollectionUtils.isEmpty(dropOffMap)) {
            RecentConfig config = new RecentConfig();
            config.setIncludeOperatorInKeyServices(includeOperatorInKeyServices);
            wrapper.setData(prepareFavResponse(recents, dropOffMap, config, request));
        } else {
            logger.debug("Recent Data fetch from db and dropoff  fetched also empty");
        }


        return wrapper;
    }

    protected abstract void updateSortedRecentsAndDropoff(List<Recents> recents, Map<String, DropOffResponse> dropOffMap, FrequentOrderRequest request, FrequentOrderResponseWrapper wrapper) throws RechargeSagaBaseException;

    protected abstract List<Recents> getRecentDataFromDB(FrequentOrderRequest request);

    protected abstract void handleFastagRechargeNumber(List<Recents> recents);

    protected abstract void revertFastagRechargeNumber(List<Recents> recents, Map<String, Recents> filteredRecents, Map<String, Recents> txnStatus, Map<String, Recents> txnStatusForNonRU, RecentConfig config);


    protected boolean filterForSMSEnabledService(Recents recent, FrequentOrderRequest request) {
        boolean isCardEnable = !Constants.EVENT_SOURCE.SMS.equalsIgnoreCase(recent.getEventSource())
                || (Constants.EVENT_SOURCE.SMS.equalsIgnoreCase(recent.getEventSource())
                    && recent.getOrderId() != null)
                || (serviceConfig.enableSMSCardInRecent()
                    && serviceConfig.getSMSEnabledServices().contains(StringUtils.lowerCase(recent.getKey().getService()))
                    && isSmsLive(recent.getKey().getService(), request));
        if (Constants.SERVICE_MOBILE.equalsIgnoreCase(recent.getKey().getService())) {
            return !Constants.EVENT_SOURCE.SMS.equalsIgnoreCase(recent.getEventSource())
                    || (Constants.EVENT_SOURCE.SMS.equalsIgnoreCase(recent.getEventSource())
                        && recent.getOrderId() != null)
                    || ((serviceConfig.enableSMSCardInRecent()
                        && serviceConfig.getSMSEnabledServices().contains(StringUtils.lowerCase(recent.getKey().getService()))
                        && isSmsLive(recent.getKey().getService(), request)) && RecentUtils.isWhitelistedCustIdForMobileNonRU(recent.getKey().getCustomerId(), serviceConfig));

        }
        return isCardEnable;
    }


    private boolean isSmsLive(String service, FrequentOrderRequest request) {


        Map<String, List<Integer>> smsCategoryMap = serviceConfig.getCategoryMapping();


        String smsLiveConfig = FeatureConfigCache.getInstance().getString(IS_SMS_LIVE);

        if (Objects.isNull(smsLiveConfig)) {
            logger.info("featureConfigs:: no config found for featureName {}", IS_SMS_LIVE);
            return false;
        }


        return liveOnClientVersion(new JSONObject(smsLiveConfig), new HashSet<>(smsCategoryMap.get(service)), request);

    }


    private boolean liveOnClientVersion(JSONObject config, Set<Integer> ids, FrequentOrderRequest request) {
        for (Integer id : ids) {
            logger.debug("featureConfigs:: inside liveOnClientVersion live for configMap {} ids {} client {} version {}", config, id, request.getClient(), request.getVersion());
            if (DropOffUtils.checkForClientVersion(config, request, Long.valueOf(id))) {
                return true;
            }
        }
        return false;
    }

    private List<FrequentOrderResponse> prepareFavResponse(List<Recents> recents, Map<String, DropOffResponse> dropOffMap, RecentConfig config, FrequentOrderRequest request) {
        logger.info("prepareFavResponse starts , recent size {} , dropOffMap size {}", recents.size(), dropOffMap.size());


        List<FrequentOrderResponse> favResponse = new ArrayList<>();

        handleFastagRechargeNumber(recents);

        Map<String, DropOffResponse> dropOffUniqueMap = DropOffUtils.getByRecentUniquenessLogic(dropOffMap, config, request);
        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRU = new HashMap<>();
        Map<Long, Integer> categoryWiseCount = new HashMap<>();
        Map<String, Recents> filteredRecents = getFilteredRecents(recents, config, request, txnStatus,txnStatusForNonRU);

        revertFastagRechargeNumber(recents, filteredRecents, txnStatus, txnStatusForNonRU, config);


        pushCountToDD(FREQUENT_ORDER, recents.size(), PrometheusConstants.STATE.RECENT_SIZE);
        pushCountToDD(FREQUENT_ORDER, dropOffMap.size(), PrometheusConstants.STATE.DROPOFF_SIZE);

        pushCountToDD(FREQUENT_ORDER, recents.size() - filteredRecents.size(), PrometheusConstants.STATE.RECENTS_TO_FILTERED_RECENT_DIFF);


        logger.info("filteredRecents size is {} ", filteredRecents.size());

        updateLocalisationConfig(config, request.getExcludeDropoff());

        for (Map.Entry<String, Recents> recentEntry : filteredRecents.entrySet()) {

            String key = recentEntry.getKey();
            Recents recent = recentEntry.getValue();

            logger.trace("key is {}", key);

            boolean prepaidTxnExists = checkIfPrepaidTXNExist(recent, txnStatus, config);
            if(prepaidTxnExists){
                Map<String, Recents> uniqueMap = new HashMap<>();
                uniqueMap.put(key,txnStatus.get(getTxnMapKey(recent, config)));
                this.setOperatorValidatedAt(uniqueMap,key,recent);
            }
            ProductMin product = CVRProductCache.getInstance().getProductDetails(recent.getProductId());
            DropOffResponse dropOff = dropOffUniqueMap.get(key);
            FrequentOrderResponse response = null;

            if (CVRProductCache.getInstance().isInactivePID(recent.getProductId())) {
                logger.info("reco invalid PID for customer id {} recharge number {} service {} operator {} trxStatus {} PID {}", recent.getKey().getCustomerId(), recent.getKey().getRechargeNumber(), recent.getKey().getService(), recent.getKey().getOperator(), txnStatus.containsKey(getTxnMapKey(recent, config)), recent.getProductId());
                pushCountToDD("RECO_INVALID_PID", 1, recent.getKey().getService());
                continue;
            }
            if (isDropOffEligible(product, dropOff, config, request, recent.getOrderId()) && filterBasedOnFlag(recent, request, isExpiryWithinRange(dropOff))) {
                logger.info("Building fav response from dropoff, recharge number {}", recent.getKey().getRechargeNumber());
                response = build(dropOff, recent, isExpiryWithinRange(dropOff));

                response.setBillVisibilityDays(serviceConfig.getBillVisiblityDays());
            } else if (isRecentBillValid(recent, request, prepaidTxnExists)) {

                logger.info("Building fav response from recent, recharge number {}", recent.getKey().getRechargeNumber());
                response = FrequentOrderResponseBuilder.recentToFrequentOrderResponse(recent, metricsHelper,serviceConfig);
                response.setBillVisibilityDays(serviceConfig.getBillVisiblityDays(recent.getKey().getService()));
            } else {
                logger.info("reco order id empty for customer id {} recharge number {} service {} operator {} trxStatus {}", recent.getKey().getCustomerId(), recent.getKey().getRechargeNumber(), recent.getKey().getService(), recent.getKey().getOperator(), txnStatus.containsKey(getTxnMapKey(recent, config)));
                pushCountToDD("RECO_EMPTY_ORDER_ID", 1, recent.getKey().getService());
            }


            if (recent.getProductId() != null && skipBasedOnCategory(categoryWiseCount, recent.getProductId())) {
                logger.info("skipping key as recent category limit reached");
                continue;
            }

            if (response != null){
                if(Boolean.TRUE.equals(request.getIsCardSkinRequired())) {
                   CardDetails cardDetails = null;
                   if(recent.getCardVariant()!=null)
                       cardDetails = FeatureConfigCache.getInstance().getCardVariantSkinDetails(recent.getCardVariant());
                   if(cardDetails!=null){
                       if(Boolean.TRUE.equals(cardDetails.getKeepCardSkin()))
                           response.setMediaAssets(RecentUtils.createCardSkin(recent.getCardSkin()));
                       if(Boolean.TRUE.equals(cardDetails.getKeepCardVariant()))
                           response.setIssuingBankCardVariant(recent.getCardVariant());
                   }else {
                       response.setMediaAssets(RecentUtils.createCardSkin(recent.getCardSkin()));
                       response.setIssuingBankCardVariant(recent.getCardVariant());
                   }
                }

                if(Objects.isNull(response.getBillState())
                        || BillState.NO_DUE.compareTo(response.getBillState())==0 || BillState.NO_DUE_OUT_AMT.compareTo(response.getBillState())==0){
                    response.setEarlyPayment(false);
                }

                if(Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType())&&Constants.SERVICE_MOBILE.equalsIgnoreCase(recent.getKey().getService())){
                    response.setPlanBucket(RecentUtils.getPlanBucket(recent));
                }
//                sendEventTypeCombinationMetrics(response);
                addToFavResponse(favResponse, response, categoryWiseCount);
            }

            dropOffUniqueMap.remove(key);
        }

        pushCountToDD(FREQUENT_ORDER, filteredRecents.size() - favResponse.size(), PrometheusConstants.STATE.RECENTS_TO_FINAL_RECENT_DIFF);
        if (Boolean.FALSE.equals(serviceConfig.disableDropOffService()))
            addDropOffInFavResponse(dropOffUniqueMap, favResponse, request, config);

        dedupeWithSMSCard(favResponse, recents, request,txnStatusForNonRU);

        return favResponse;
    }

    private void dedupeWithSMSCard(List<FrequentOrderResponse> favResponse, List<Recents> recents, FrequentOrderRequest request, Map<String, Recents> txnStatus) {

        Map<String, Recents> smsCardsMap = new LinkedHashMap<>();
        Set<Integer> cardLengths = new HashSet<>();
        Set<Integer> recentCardLengths = new HashSet<>();
        Map<String, FrequentOrderResponse> recentCardsMap = new LinkedHashMap<>();
        Map<String,String> mapping=new LinkedHashMap<>();
        Map <String,Recents> deduplicatedElectricityMap=new LinkedHashMap<>();
        updateSMSCardMap(recents, smsCardsMap, cardLengths,deduplicatedElectricityMap);
        updateSMSCardMapForLoan(recents, smsCardsMap);
        updateRecentCardMap(favResponse, recentCardsMap, recentCardLengths);
        removeSMSCardBasedOnDropOffKey(recentCardsMap, smsCardsMap, cardLengths);
        deDupAirtelPrepaidCSVCards(smsCardsMap);
        smsCardsMap = removeSMSCardMapIfKeyExistInSMSCard(recentCardsMap, smsCardsMap, recentCardLengths);

        try {
            for (Map.Entry<String, Recents> entry : txnStatus.entrySet()) {
                Recents recentCardForMobile = entry.getValue();
                if (Constants.SERVICE_MOBILE.equalsIgnoreCase(recentCardForMobile.getKey().getService()) && Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recentCardForMobile.getPayType())) {
                    String key = getDedupeKeyForSMS(entry.getValue());

                    if (Objects.nonNull(smsCardsMap.get(key)) && entry.getValue().getTxnTime().getTime() > smsCardsMap.get(key).getUpdatedAt().getTime()) {
                        logger.info("dedupeWithSMSCard::key for removing txn card from smsCardMap {}", key);
                        smsCardsMap.remove(getDedupeKeyForSMS(entry.getValue()));
                    }else if (Objects.nonNull(smsCardsMap.get(key)) && Service.MOBILE.value.equalsIgnoreCase(recentCardForMobile.getKey().getService())){
                        Map<String, Recents> uniqueMap = new LinkedHashMap<>();
                        uniqueMap.put(key,recentCardForMobile);
                        this.setOperatorValidatedAt(uniqueMap,key,smsCardsMap.get(key));
                    }
                }

            }

        } catch (Exception ex) {
            logger.error("dedupeWithSMSCard::error while deleting card from smsCardMap ", ex);
        }






        smsCardsMap.values().stream().peek(recent -> {
                    Long activePid = ActiveInactivePidMapCache.getInstance().getActivePid(recent.getProductId());
                    recent.setProductId(activePid);
                })
                .filter(recent -> !CVRProductCache.getInstance().isInactivePID(recent.getProductId()) && !isSkippable(recent, request))
                .forEach(recent -> {
                    updateWidgetSpecificFields(recent);
                    if (filterBasedOnFlag(recent, request)){
                        if (Service.MOBILE.value.equalsIgnoreCase(recent.getKey().getService())){
                            Map<String, Recents> uniqueMap = new LinkedHashMap<>();
                            if(recent.getOperatorValidationDto() ==null || recent.getOperatorValidationDto().getDate() ==null)
                                this.setOperatorValidatedAt(uniqueMap,recent.getKey().getRechargeNumber(),recent);
                        }
                        addSmsCardToFavResponse(favResponse,FrequentOrderResponseBuilder.recentToFrequentOrderResponse(recent, metricsHelper,serviceConfig));
                    }

                        //favResponse.add(recentToFrequentOrderResponse.apply(recent));
                });
    }

    private void deDupAirtelPrepaidCSVCards(Map<String, Recents> smsCardsMap) {
        Map<String, Recents> airtelCardsMap = new HashMap<>();
        Iterator<String> iterator = smsCardsMap.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Recents recent = smsCardsMap.get(key);
            if(isAirtelPrepaidCSVCard(recent)) {
                airtelCardsMap.put(key, recent);
                iterator.remove();
            }
        }

        for(Map.Entry<String, Recents> entry : smsCardsMap.entrySet()) {
            Recents recent = entry.getValue();
            String airtelDedupedKey = getDedupeKeyForAirtelPrepaidCSVFromSMSRecent(recent);
            if(!StringUtils.isEmpty(airtelDedupedKey)) {
                if(airtelCardsMap.containsKey(airtelDedupedKey)) {
                    airtelCardsMap.remove(airtelDedupedKey);
                }
            }
        }

        if(!airtelCardsMap.isEmpty()) {
            smsCardsMap.putAll(airtelCardsMap);
        }
    }

    private boolean isAirtelPrepaidCSVCard(Recents recent) {
        return Constants.SERVICE_MOBILE.equalsIgnoreCase(recent.getKey().getService())
                && Constants.OPERATOR_AIRTEL.equalsIgnoreCase(recent.getKey().getOperator())
                && Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType())
                && Constants.EVENT_SOURCE.CSV.equalsIgnoreCase(recent.getEventSource())
                && (recent.getDueAmount() == null || recent.getDueAmount() == 0);
    }


    private void updateRecentCardMap(List<FrequentOrderResponse> recentCardMap, Map<String, FrequentOrderResponse> recentCardsMap, Set<Integer> cardLengths) {

        for (FrequentOrderResponse response : recentCardMap) {
            if(EventType.SMART_RECENT.equals(response.getEventType())) {
                continue;
            }
            cardLengths.add(removeXCharacter(response.getRechargeNumber1()).length());
            String dedupedKey = getDedupeKeyForFrequentOrder(response, -1);
            if (Objects.nonNull(dedupedKey) && !dedupedKey.isEmpty()) {
                recentCardsMap.put(dedupedKey, response);

            }
        }

    }

    private Map<String, Recents> removeSMSCardMapIfKeyExistInSMSCard(Map<String, FrequentOrderResponse> frequentOrderResponseMap, Map<String, Recents> smsCardsMap, Set<Integer> frequentCardLengths) {
        Set<String> keys = smsCardsMap.keySet();
        Map<String, Recents> smsCardUpdartedMap = new HashMap<>(smsCardsMap);
        for (String k : keys) {
            String smsCardDedupedKey = null;
            for (int limit : frequentCardLengths) {
                smsCardDedupedKey = getDedupeKeyRecent(smsCardsMap.get(k), limit);
                if (Objects.nonNull(smsCardDedupedKey) && !smsCardDedupedKey.isEmpty()) {
                    if (frequentOrderResponseMap.containsKey(smsCardDedupedKey)) {
                        smsCardUpdartedMap.remove(k);
                        break;
                    }
                }
            }
        }
        return smsCardUpdartedMap;

    }

    private void removeSMSCardBasedOnDropOffKey(Map<String, FrequentOrderResponse> recentCardMap,
            Map<String, Recents> smsCardsMap,
            Set<Integer> cardLengths) {

        if (smsCardsMap.isEmpty()) {
            return;
        }

        for (Map.Entry<String, FrequentOrderResponse> recentEntry : recentCardMap.entrySet()) {
            FrequentOrderResponse recentCard = recentEntry.getValue();

            handleDeduplicacyInCard(recentCard, smsCardsMap, cardLengths);

        }
    }



    private void handleDeduplicacyInCard(FrequentOrderResponse recentCard,
                                         Map<String, Recents> smsCardsMap,
                                         Set<Integer> cardLengths) {
        String frequentDedupedKey = null;
        // Handle regular deduplication
        for (int limit : cardLengths) {
            frequentDedupedKey = getDedupeKeyForFrequentOrder(recentCard, limit);
            if (Objects.nonNull(frequentDedupedKey) && !frequentDedupedKey.isEmpty()) {
                if (smsCardsMap.containsKey(frequentDedupedKey)) {

                    smsCardsMap.remove(frequentDedupedKey);
                    break;
                }
            }
        }

        // Handle Airtel prepaid CSV specific deduplication
        handleAirtelPrepaidCSVDeduplication(recentCard, smsCardsMap);
    }

    private void handleAirtelPrepaidCSVDeduplication(FrequentOrderResponse recentCard,
            Map<String, Recents> smsCardsMap) {

        String smsDedupedKeyForAirtelPrepaidCSV = getDedupeKeyForAirtelPrepaidCSV(recentCard);
        if (StringUtils.isNotEmpty(smsDedupedKeyForAirtelPrepaidCSV)) {
            if (smsCardsMap.containsKey(smsDedupedKeyForAirtelPrepaidCSV)
                    && Constants.EVENT_SOURCE.CSV.equalsIgnoreCase(
                            smsCardsMap.get(smsDedupedKeyForAirtelPrepaidCSV).getEventSource())) {
                smsCardsMap.remove(smsDedupedKeyForAirtelPrepaidCSV);
            }
        }
    }



    private void updateSMSCardMap(List<Recents> recents, Map<String, Recents> smsCardsMap, Set<Integer> cardLengths,Map <String,Recents> deduplicatedElectricityMap) {
        Map<String,String> mapping=new HashMap<>();
        for (Recents recent : recents) {


            if ((!StringUtils.equalsAnyIgnoreCase(recent.getEventSource(),Constants.EVENT_SOURCE.SMS,Constants.EVENT_SOURCE.CSV) &&
                   ! isSMSEventSourcePresentForMobile(recent)) || recent.getKey().getService().equalsIgnoreCase(Constants.ServiceTypeConstants.LOAN)) {
                continue;
            }

            if (Constants.FINANCIAL_SERVICES.equalsIgnoreCase(recent.getKey().getService())) {
                recent.setMcn(recent.getKey().getRechargeNumber());
            }
            cardLengths.add(removeXCharacter(recent.getKey().getRechargeNumber()).length());

            String smsDedupedKey = getDedupeKeyForSMS(recent);
            if (Objects.nonNull(smsDedupedKey) && !smsDedupedKey.isEmpty() && !smsCardsMap.containsKey(smsDedupedKey)) {
                Recents mergedRecentCard=recent;
                String deduplicationKey=null;
                if (recent.getKey().getService().equalsIgnoreCase(ELECTRICITY.value) && serviceConfig.getWhielistOperatorServicesForDedup().contains(recent.getKey().getOperator().toLowerCase())) {

                     deduplicationKey = generateDuplicationKeyForRechargeNumber(recent);
                     logger.info("updateSMSCardMap::smsDedupedKey :: {}", deduplicationKey);
                        if (deduplicatedElectricityMap.containsKey(deduplicationKey)) {
                        Recents existingRecent = deduplicatedElectricityMap.get(deduplicationKey);
                        try{
                            mergedRecentCard = mergeDuplicateElectricityCard(existingRecent, recent);
                            pushCountToDD("MERGED_DEDUPED_SMS_CARD", 1, recent.getKey().getService());

                        } catch(Exception e){
                            logger.error("updateSMSCardMap::Error while merging deduping cards",e);
                        }




                        }
                    deduplicatedElectricityMap.put(deduplicationKey, mergedRecentCard);



                }
                if(mapping.get(deduplicationKey)!=null
                        && recent.getKey().getService().equalsIgnoreCase(ELECTRICITY.value)
                        && serviceConfig.getWhielistOperatorServicesForDedup().contains(recent.getKey().getOperator().toLowerCase())){
                    smsCardsMap.put(mapping.get(deduplicationKey), mergedRecentCard);

                }
                else{
                    smsCardsMap.put(smsDedupedKey, mergedRecentCard);
                }

                if(recent.getKey().getService().equalsIgnoreCase(ELECTRICITY.value)
                        && (serviceConfig.getWhielistOperatorServicesForDedup().contains(recent.getKey().getOperator().toLowerCase()))) {
                    mapping.put(deduplicationKey,smsDedupedKey);

                }


            }
        }
    }

    private void updateSMSCardMapForLoan(List<Recents> recents, Map<String, Recents> smsCardsMap) {
        HashSet<String> nonSMSDeDuplicationKeys = new HashSet<>();
        for (Recents recent : recents) {
            if(recent.getKey().getService().equalsIgnoreCase(Constants.ServiceTypeConstants.LOAN)
            && !StringUtils.equalsAnyIgnoreCase(recent.getEventSource(),Constants.EVENT_SOURCE.SMS,Constants.EVENT_SOURCE.RU_SMS,Constants.EVENT_SOURCE.CSV)){
                nonSMSDeDuplicationKeys.add(getDeDuplicationKeyForLoanSMS(recent));
            }
        }
        for (Recents recent : recents) {
            if (!recent.getKey().getService().equalsIgnoreCase(Constants.ServiceTypeConstants.LOAN))
                continue;
            if (!StringUtils.equalsAnyIgnoreCase(recent.getEventSource(),Constants.EVENT_SOURCE.SMS,Constants.EVENT_SOURCE.RU_SMS,Constants.EVENT_SOURCE.CSV))
                continue;
            String smsDeDuplicationKey = getDeDuplicationKeyForLoanSMS(recent);
            if (nonSMSDeDuplicationKeys.contains(smsDeDuplicationKey))
                continue;
            if(!smsCardsMap.containsKey(smsDeDuplicationKey))
                smsCardsMap.put(smsDeDuplicationKey, recent);
            else if (smsCardsMap.get(smsDeDuplicationKey).getKey().getRechargeNumber().equalsIgnoreCase(RecentUtils.generateDummyRNForLoan(
                                    smsCardsMap.get(smsDeDuplicationKey).getKey().getCustomerId().toString(),
                                    smsCardsMap.get(smsDeDuplicationKey).getKey().getOperator()))) {
                smsCardsMap.put(smsDeDuplicationKey, recent);
            }
        }
    }

    private String getDedupeKeyForAirtelPrepaidCSVFromSMSRecent(Recents recents) {
        if (Objects.isNull(recents))
            return null;

        if (Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recents.getPayType()) && Constants.SERVICE_MOBILE.equalsIgnoreCase(recents.getKey().getService())
                && Constants.OPERATOR_AIRTEL.equalsIgnoreCase(recents.getOperator())) {
            return recents.getKey().getRechargeNumber() +
                    recents.getKey().getService();
        }
        return null;
    }

    private String getDedupeKeyForAirtelPrepaidCSV(FrequentOrderResponse frequentOrderResponse) {
        if (Objects.isNull(frequentOrderResponse))
            return null;

        if (frequentOrderResponse.getPid() == null) {
            return null;
        }

        ProductMin product = CVRProductCache.getInstance().getProductDetails(Long.parseLong(frequentOrderResponse.getPid()));
        if(product == null) {
            return null;
        }
        if (Constants.PREPAID_PAYTYPE.equalsIgnoreCase(product.getPayType()) && Constants.SERVICE_MOBILE.equalsIgnoreCase(product.getService())
            && Constants.OPERATOR_AIRTEL.equalsIgnoreCase(product.getOperator())) {
            return frequentOrderResponse.getRechargeNumber1() +
                    product.getService();
        }
        return null;
    }


    private String getDedupeKeyForFrequentOrder(FrequentOrderResponse frequentOrderResponse, int length) {
        if (Objects.isNull(frequentOrderResponse))
            return null;

        if (frequentOrderResponse.getPid() == null) {
            return null;
        }

        ProductMin product = CVRProductCache.getInstance().getProductDetails(Long.parseLong(frequentOrderResponse.getPid()));
        if (Constants.PREPAID_PAYTYPE.equalsIgnoreCase(product.getPayType()) && Constants.SERVICE_MOBILE.equalsIgnoreCase(product.getService())) {
            return frequentOrderResponse.getRechargeNumber1() +
                    product.getService() + frequentOrderResponse.getPlanBucket();
        }
        String cardNumber = removeXCharacter(frequentOrderResponse.getRechargeNumber1());
        if (length == -1)
            length = cardNumber.length();
        if (cardNumber.length() < length) {
            return null;
        }
        return cardNumber.substring(cardNumber.length() - length) +
                getIssuingBankName(Long.valueOf(frequentOrderResponse.getPid()));
    }


    private String getDedupeKeyRecent(Recents recent, int length) {
        if (Objects.isNull(recent))
            return null;

        if (recent.getProductId() == null) {
            return null;
        }
        String rechargeNumber = null;
        if (PayType.CREDIT_CARD.value.equalsIgnoreCase(recent.getPayType()))
            rechargeNumber = recent.getMcn();
        else
            rechargeNumber = recent.getKey().getRechargeNumber();

        String cardNumber = removeXCharacter(rechargeNumber);
        if (cardNumber.length() < length) {
            return null;
        }
        else if (Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType()) && Constants.SERVICE_MOBILE.equalsIgnoreCase(recent.getKey().getService())) {
            return recent.getKey().getRechargeNumber() +
                    recent.getKey().getService() + RecentUtils.getPlanBucket(recent) ;
        }
        return cardNumber.substring(cardNumber.length() - length) +
                getIssuingBankName(Long.valueOf(recent.getProductId()));
    }

    private String getDedupeKeyForSMS(Recents recent) {


        if (Objects.isNull(recent.getProductId()))
            return null;

        String rechargeNumber = null;
        if (PayType.CREDIT_CARD.value.equalsIgnoreCase(recent.getPayType())) {
            return removeXCharacter(recent.getMcn()) +
                    getIssuingBankName(recent.getProductId());
        }
        else if (Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType()) && Constants.SERVICE_MOBILE.equalsIgnoreCase(recent.getKey().getService())) {
            return recent.getKey().getRechargeNumber() +
                    recent.getKey().getService() + RecentUtils.getPlanBucket(recent) ;
        }else {
            return removeXCharacter(recent.getKey().getRechargeNumber()) +
                    recent.getKey().getService();
        }


    }

    private String getDeDuplicationKeyForLoanSMS(Recents recent) {
        return recent.getKey().getService() + recent.getKey().getOperator() ;
    }


    private String removeXCharacter(String rechargeNumber) {
        String s = rechargeNumber.replaceAll("\\s+", "").replaceAll("X", "").replace("x", "");
        return s;
    }

    private boolean isExpiryWithinRange(DropOffResponse dropOff) {
        if (CollectionUtils.isEmpty(dropOff.getBills()))
            return false;
        DropOffBillsObject dropOffBillsObject = dropOff.getBills().get(0);

        String expiryDays = StringUtils.firstNonEmpty(dropOffBillsObject.getDue_date(), dropOffBillsObject.getExpiry());
        if (expiryDays == null)
            return false;

        return isExpiryWithinRange(dropOff.getPaytype(), dropOff.getService(), DateUtil.stringToDate(expiryDays, DateFormats.DATE_TIME_FORMAT_2));
    }

    private boolean checkIfPrepaidTXNExist(Recents recent, Map<String, Recents> txnStatus, RecentConfig config) {
        if ((Objects.isNull(recent.getProductId())
                || Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType())) && Objects.isNull(recent.getOrderId()) && (Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType()) && (recent.getIsValidation() == null || Boolean.TRUE.equals(!recent.getIsValidation())))) {
            Recents txnRecent = txnStatus.get(getTxnMapKey(recent, config));
            if (Objects.nonNull(txnRecent)) {
                if (Objects.isNull(recent.getProductId()))
                    recent.setProductId(ActiveInactivePidMapCache.getInstance().getActivePid(txnRecent.getProductId()));
                recent.setConsumerName(txnRecent.getConsumerName());
                recent.setNickName(txnRecent.getNickName());
                recent.setCylinderAgencyName(txnRecent.getCylinderAgencyName());
                recent.setTxnAmount(txnRecent.getTxnAmount());
                recent.setOrderId(txnRecent.getOrderId());
                recent.setTxnTime(txnRecent.getTxnTime());
                return true;
            }

        }

        return false;
    }

    protected abstract boolean skipBasedOnCategory(Map<Long, Integer> categoryWiseCount, Long productId);

    protected abstract void addToFavResponse(List<FrequentOrderResponse> favResponse, FrequentOrderResponse response, Map<Long, Integer> categoryWiseMap);

    /**
     * The method filter out recents  based on below conditions :
     * 1. Invalid Pid
     * 2. If mcn is provided in request then return recent only for that
     *
     * @param recents
     * @param config
     * @param request
     * @return
     */
    public Map<String, Recents> getFilteredRecents(List<Recents> recents, RecentConfig config, FrequentOrderRequest request, Map<String, Recents> txnStatus,Map<String, Recents> txnStatusForNonRu) {

        logger.info("RecentUtils getFilteredRecents starts");

        Map<String, Recents> uniqueMap = new LinkedHashMap<>();
        Map<String, Recents> deduplicatedElectricityMap = new LinkedHashMap<>();
        Map<String, Recents> deduplicatedElectricitySmsMap = new LinkedHashMap<>();
        Map<String, String> mapping = new LinkedHashMap<>();
        for (Recents recent : recents) {
            if (StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), Constants.EVENT_SOURCE.SMS, Constants.EVENT_SOURCE.CSV) && recent.getOrderId() == null){
                if (recent.getKey().getService().equalsIgnoreCase(ELECTRICITY.value)
                        && (serviceConfig.getWhielistOperatorServicesForDedup().contains(recent.getKey().getOperator().toLowerCase()))) {

                    deduplicatedElectricitySmsMap.put(generateDuplicationKeyForRechargeNumber(recent), recent);


                }

                continue;
            }
            else{
                if (recent.getKey().getService().equalsIgnoreCase(ELECTRICITY.value)
                        && (serviceConfig.getWhielistOperatorServicesForDedup().contains(recent.getKey().getOperator().toLowerCase()))) {
                    String deduplicationKeyForSms = generateDuplicationKeyForRechargeNumber(recent);
                    logger.info("getFilteredRecents::deduplicationKey for Sms Card  {}", deduplicationKeyForSms);
                    if (deduplicatedElectricitySmsMap.containsKey(deduplicationKeyForSms)) {
                        Recents existingRecent = deduplicatedElectricitySmsMap.get(deduplicationKeyForSms);
                        try{
                            Recents mergedRecentCardSms = mergeDuplicateElectricityCard(existingRecent, recent);
                            mergedRecentCardSms.setEventSource(recent.getEventSource());
                            recent=mergedRecentCardSms;
                            pushCountToDD("MERGED_DEDUPED_SMS_CARD", 1, recent.getKey().getService());


                        }
                        catch(Exception e){
                            logger.error("getFilteredRecents::Error while merging deduping cards",e);
                        }



                    }
                }



            }

            recent.setProductId(ActiveInactivePidMapCache.getInstance().getActivePid(recent.getProductId()));
            if (PayType.CREDIT_CARD.value.equalsIgnoreCase(recent.getPayType()) && Boolean.TRUE.equals(request.getIsCoft())) {
                recent.setMcn(RecentUtils.maskMCN(recent.getMcn()));
            }
            if (recent.getOrderId() != null) {
                txnStatus.put(getTxnMapKey(recent, config), recent);
                txnStatusForNonRu.put(getTxnMapKeyForNonRU(recent, config), recent);
            }
            if (isSkippable(recent, request) || RecentUtils.skipIfMCNMismatch(request, recent))
                continue;
            String key = getUniqueKey(recent, config);

            updateWidgetSpecificFields(recent);
            if (updateInUniqueMap(uniqueMap, key, recent)) {
                if (Service.MOBILE.value.equalsIgnoreCase(recent.getKey().getService())) {
                    this.setOperatorValidatedAt(uniqueMap, key, recent);
                }
                Recents mergedRecentCard = recent;
                String deduplicationKey=null;

                if (recent.getKey().getService().equalsIgnoreCase(ELECTRICITY.value)
                        && (serviceConfig.getWhielistOperatorServicesForDedup().contains(recent.getKey().getOperator().toLowerCase()))) {

                    deduplicationKey = generateDuplicationKeyForRechargeNumber(recent);
                    logger.info("getFilteredRecents::deduplicationKey for recent {}", deduplicationKey);
                    if (deduplicatedElectricityMap.containsKey(deduplicationKey)) {
                        Recents existingRecent = deduplicatedElectricityMap.get(deduplicationKey);
                        try{
                            mergedRecentCard = mergeDuplicateElectricityCard(existingRecent, recent);
                            pushCountToDD("MERGED_DEDUPED_CARD", 1, recent.getKey().getService());

                        }
                        catch(Exception e){
                           logger.error("getFilteredRecents::Error while merging deduping cards",e);
                        }


                    }
                    deduplicatedElectricityMap.put(deduplicationKey, mergedRecentCard);


                }
                if(mapping.get(deduplicationKey)!=null &&
                        recent.getKey().getService().equalsIgnoreCase(ELECTRICITY.value)
                        && (serviceConfig.getWhielistOperatorServicesForDedup().contains(recent.getKey().getOperator().toLowerCase()))) {
                    uniqueMap.put(mapping.get(deduplicationKey), mergedRecentCard);
                }
                else{
                    uniqueMap.put(key, mergedRecentCard);
                }

                if(recent.getKey().getService().equalsIgnoreCase(ELECTRICITY.value)
                        && (serviceConfig.getWhielistOperatorServicesForDedup().contains(recent.getKey().getOperator().toLowerCase()))) {

                    mapping.put(deduplicationKey, key);
                    deduplicationKey=null;
                }

            }

        }
        return uniqueMap;
    }

    private String generateDuplicationKeyForRechargeNumber(Recents recents) {
        return RecentUtils.getTrimmedRechargeNumber(recents.getKey().getRechargeNumber()) + recents.getKey().getService().toLowerCase() + recents.getKey().getOperator().toLowerCase();

    }


    private Recents mergeDuplicateElectricityCard(Recents existingRecent, Recents recent) {
        // First determine which card has the latest bill details
        Recents cardWithLatestBill;
        if (existingRecent.getBillDate() != null && recent.getBillDate() != null) {
            cardWithLatestBill = existingRecent.getBillDate().after(recent.getBillDate()) ? existingRecent : recent;
        } else {
            cardWithLatestBill = existingRecent.getBillDate() != null ? existingRecent : recent;
        }
        Recents cardWithLatestBillUpdateTime = (existingRecent.getBillUpdateTime()!=null && recent.getBillUpdateTime()!=null &&existingRecent.getBillUpdateTime().compareTo(recent.getBillUpdateTime()) >= 0 )? existingRecent : recent;
        Recents primary = null;
        if (cardWithLatestBill == cardWithLatestBillUpdateTime) {
            primary = cardWithLatestBill;
        }else if(existingRecent.getBillDate() != null && recent.getBillDate() != null && existingRecent.getBillDate().equals(recent.getBillDate())){
            primary=cardWithLatestBillUpdateTime;
        }
        else {
            primary = cardWithLatestBill;
        }
        Recents secondary = (primary == existingRecent) ? recent : existingRecent;

        // Handle mark as paid updates based on conditions
        handleMarkAsPaidUpdates(primary, secondary);

        if (secondary.getUpdatedAt()!=null && primary.getUpdatedAt()!=null && secondary.getUpdatedAt().compareTo(primary.getUpdatedAt()) > 0) {
            primary.setUpdatedAt(secondary.getUpdatedAt());
        }

        Date latestTxnTimeSecondary=RecentUtils.getLastTxnTime(secondary);
        Date latestTxnTimePrimary=RecentUtils.getLastTxnTime(primary);
        if ((Objects.isNull(latestTxnTimePrimary)   || (Objects.nonNull(latestTxnTimeSecondary) && latestTxnTimeSecondary.compareTo(latestTxnTimePrimary) > 0))) {
            updateTransactionDetails(primary, secondary);
        }

        updateAutomaticAndNotificationStatus(primary, secondary);

        return primary;
    }

    private void handleMarkAsPaidUpdates(Recents primary, Recents secondary) {

        try{
            // Case 1: Both cards have mark as paid dates
            if (primary.getMarkAsPaidTime() != null && secondary.getMarkAsPaidTime() != null) {
                // Use mark as paid details from card with latest mark as paid date
                if (secondary.getMarkAsPaidTime().after(primary.getMarkAsPaidTime())) {
                    updateMarkAsPaidDetails(primary, secondary);
                }
                return;
            }

            // Case 2: Neither card has mark as paid - do nothing
            if (primary.getMarkAsPaidTime() == null && secondary.getMarkAsPaidTime() == null) {
                return;
            }

            // Case 3: One has mark as paid
            // Get the card with mark as paid
            Recents cardWithMarkAsPaid = primary.getMarkAsPaidTime() != null ? primary : secondary;
            Recents otherCard = primary.getMarkAsPaidTime() != null ? secondary : primary;

            // Find max bill date between both cards
            Date maxBillDate = getMaxDate(primary.getBillDate(), secondary.getBillDate());
            if (maxBillDate == null) {
                return; // Can't proceed without a bill date
            }

            // Find max txn date between both cards
            Date maxTxnDate = getMaxDate(primary.getTxnTime(), secondary.getTxnTime());

            // If txnDate is present, check both conditions
            if (maxTxnDate != null) {
                if (maxTxnDate.before(maxBillDate)) {
                    if (cardWithMarkAsPaid.getMarkAsPaidTime().after(maxBillDate)) {
                        // If secondary has mark as paid, update primary
                        if (secondary == cardWithMarkAsPaid) {
                            updateMarkAsPaidDetails(primary, secondary);
                        }
                    }
                }

            } else {
                // If no txnDate, only check if mark as paid is after bill date
                if (cardWithMarkAsPaid.getMarkAsPaidTime().after(maxBillDate)) {
                    // If secondary has mark as paid, update primary
                    if (secondary == cardWithMarkAsPaid) {
                        updateMarkAsPaidDetails(primary, secondary);
                    }
                }
            }

        }
        catch(Exception e){
            logger.error("FrequentOrderServiceImpl :: handleMarkAsPaidUpdates", e);

        }

    }

    private Date getMaxDate(Date date1, Date date2) {
        if (date1 == null && date2 == null) return null;
        if (date1 == null) return date2;
        if (date2 == null) return date1;
        return date1.after(date2) ? date1 : date2;
    }

    private void updateTransactionDetails(Recents target, Recents source) {
        target.setTxnAmount(source.getTxnAmount());
        target.setTxnStatus(source.getTxnStatus());
        target.setLastFailureTxn(source.getLastFailureTxn());
        target.setLastPendingTxn(source.getLastPendingTxn());
        target.setTxnTime(source.getTxnTime());
        target.setOrderId(source.getOrderId());
    }

    private void updateMarkAsPaidDetails(Recents target, Recents source) {
        target.setMarkAsPaidAmount(source.getMarkAsPaidAmount());
        target.setMarkAsPaidSource(source.getMarkAsPaidSource());
        target.setMarkAsPaidTime(source.getMarkAsPaidTime());
        target.setIsMarkAsPaid(source.getIsMarkAsPaid());
    }

    private void updateAutomaticAndNotificationStatus(Recents primary, Recents secondary) {
        try{
            Date autoDatePrimary = primary.getAutomaticDate();
            Date autoDateSecondary = secondary.getAutomaticDate();
            boolean isAutoPrimary = Objects.nonNull(primary.getAutomaticStatus()) && primary.getAutomaticStatus() == 1;
            boolean isAutoSecondary = Objects.nonNull(secondary.getAutomaticStatus()) && secondary.getAutomaticStatus() == 1;

            // Get max of bill dates and txn dates for comparison
            Date maxBillDate = getMaxDate(primary.getBillDate(), secondary.getBillDate());
            Date maxTxnDate = getMaxDate(primary.getTxnTime(), secondary.getTxnTime());
            Date now = new Date();

            // Case 1: Both have automatic status as 1
            if (isAutoPrimary && isAutoSecondary) {
                primary.setAutomaticStatus(1);
                // If both have automatic dates, use max
                if (autoDatePrimary != null && autoDateSecondary != null) {
                    primary.setAutomaticDate(DateUtil.max(autoDatePrimary, autoDateSecondary));
                }
                // If only one has automatic date
                else if (autoDatePrimary != null || autoDateSecondary != null) {
                    Date autoDate = autoDatePrimary != null ? autoDatePrimary : autoDateSecondary;
                    if (autoDate.after(now)) {
                        primary.setAutomaticDate(autoDate);
                    } else if (maxBillDate != null && maxTxnDate != null &&
                            autoDate.after(maxBillDate) && autoDate.after(maxTxnDate)) {
                        primary.setAutomaticDate(autoDate);
                    } else {
                        primary.setAutomaticDate(null);
                        primary.setAutomaticStatus(0);
                    }
                }
            }
            // Case 2: Both have automatic status as 0
            else if (!isAutoPrimary && !isAutoSecondary) {
                primary.setAutomaticStatus(0);
                primary.setAutomaticDate(null);
            }
            // Case 3: One has automatic status as 1, other as 0
            else {
                Recents autoCard = isAutoPrimary ? primary : secondary;
                Date autoDate = autoCard.getAutomaticDate();

                if (autoDate != null) {
                    if (autoDate.after(now)) {
                        primary.setAutomaticStatus(1);
                        primary.setAutomaticDate(autoDate);
                    } else if (maxBillDate != null && maxTxnDate != null &&
                            autoDate.after(maxBillDate) && autoDate.after(maxTxnDate)) {
                        primary.setAutomaticStatus(1);
                        primary.setAutomaticDate(autoDate);
                    } else {
                        primary.setAutomaticStatus(0);
                        primary.setAutomaticDate(null);
                    }
                } else {
                    primary.setAutomaticStatus(0);
                    primary.setAutomaticDate(null);
                }
            }

            // Handle notification status
            if ((primary.getNotificationStatus()!=null&&primary.getNotificationStatus() == 0) || (secondary.getNotificationStatus()!=null&&secondary.getNotificationStatus() == 0)) {
                primary.setNotificationStatus(0);
            }

        }
        catch (Exception e){
            logger.error("FrequentOrderServiceImpl :: updateAutomaticAndNotificationStatus", e);
        }

    }




    public void setOperatorValidatedAt(Map<String, Recents> uniqueMap, String key, Recents recent) {
        try {
            Recents existingRecent = uniqueMap.get(key);
            OperatorValidationDto newOperatorValidationDto = operatorValidatedAt(recent);
            if (existingRecent != null) {
                OperatorValidationDto existingOperatorValidationDto = existingRecent.getOperatorValidationDto();
                if(existingOperatorValidationDto == null) {
                    existingOperatorValidationDto = new OperatorValidationDto();
                }
                newOperatorValidationDto.setDate(DateUtil.max(existingOperatorValidationDto.getDate(),newOperatorValidationDto.getDate()));

            }
            recent.setOperatorValidationDto(newOperatorValidationDto);
        } catch (Exception e) {
            logger.error("FrequentOrderServiceImpl :: setOperatorValidatedAt", e);
        }
    }
    public OperatorValidationDto operatorValidatedAt(Recents recents) {
        OperatorValidationDto operatorValidationResponse = new OperatorValidationDto();
        if(!Constants.SERVICE_MOBILE.equalsIgnoreCase(recents.getKey().getService())) {
            return operatorValidationResponse;
        }
        try {
            if (Objects.nonNull(recents.getExtra())) {
                String extra = recents.getExtra();
                JSONObject extraJson = new JSONObject(extra);
                if (extraJson.has(UPDATED_SOURCE_KEY) && !extraJson.isNull(UPDATED_SOURCE_KEY)) {
                    setDateAndSource(extraJson, operatorValidationResponse);
                }
                if (Objects.isNull(operatorValidationResponse.getDate())) {
                    operatorValidationResponse.setDate(getMaxDate(extraJson));
                    operatorValidationResponse.setSource("MAX_DATE");
                }
            }
        } catch (Exception e) {
            logger.error("FrequentOrderResponseBuilder:: operatorValidatedAt error in parsing ", e);
        }
        if (Objects.isNull(operatorValidationResponse.getDate())) {
            operatorValidationResponse.setDate(recents.getTxnTime());
            operatorValidationResponse.setSource("TXN_TIME");
        }
        if (Objects.isNull(operatorValidationResponse.getDate())) {
            logger.info("returning operatorValidatedAt null {}", recents);
        }
        return operatorValidationResponse;
    }
    public static void setDateAndSource(JSONObject jsonObject,OperatorValidationDto operatorValidationResponse) {
        String updatedDataSource = jsonObject.getString(UPDATED_SOURCE_KEY);
        List<String> smsParsingSources=Arrays.asList(Constants.SMS_PARSING_SOURCES);
        if(smsParsingSources.contains(updatedDataSource) && jsonObject.has(SMS_DATE_TIME_KEY) && !jsonObject.isNull(SMS_DATE_TIME_KEY)){
            operatorValidationResponse.setDate(formatDate(jsonObject.get(SMS_DATE_TIME_KEY)));
            operatorValidationResponse.setSource(SMS_DATE_TIME_KEY);
        }else if(TRANSACTION_SOURCE_KEY.equals(updatedDataSource) && jsonObject.has(PAYMENT_DATE_TIME_KEY) && !jsonObject.isNull(PAYMENT_DATE_TIME_KEY)){
            operatorValidationResponse.setDate(formatDate(jsonObject.get(PAYMENT_DATE_TIME_KEY)));
            operatorValidationResponse.setSource(PAYMENT_DATE_TIME_KEY);
        }else if((VALIDATION_SYNC_SOURCE_KEY.equals(updatedDataSource) || FFR_SOURCE_KEY.equals(updatedDataSource))
                && jsonObject.has(BILL_FETCH_DATE_TIME_KEY) && !jsonObject.isNull(BILL_FETCH_DATE_TIME_KEY)){
            operatorValidationResponse.setDate(formatDate(jsonObject.get(BILL_FETCH_DATE_TIME_KEY)));
            operatorValidationResponse.setSource(BILL_FETCH_DATE_TIME_KEY);
        }
    }
    private Date getMaxDate(JSONObject jsonObject){
        List<Date> dates=new ArrayList<>();
        String[] operatorValidatedAtKeys={SMS_DATE_TIME_KEY,PAYMENT_DATE_TIME_KEY,BILL_FETCH_DATE_TIME_KEY};
        try{
            for(String operatorValidatedAtKey:operatorValidatedAtKeys){
                if(jsonObject.has(operatorValidatedAtKey) && !jsonObject.isNull(operatorValidatedAtKey)){
                    dates.add(formatDate(jsonObject.get(operatorValidatedAtKey)));
                }
            }
            if(dates.isEmpty())return null;
            return dates.stream().max(Date::compareTo).get();
        }catch(Exception e){
            logger.info("error while getting getMaxDate",e);
            return null;
        }
    }
    public static Date formatDate(Object dateObj){
        if(Objects.isNull(dateObj)) return null;
        String date=dateObj.toString();
        if(Objects.nonNull(date)){
            if(StringUtils.isNumeric(date) && new Long(date)!=null){
                return new Date(new Long(date));
            }else if(DateUtil.stringToDate(date,DateFormats.DATE_TIME_FORMAT_2)!=null){
                return DateUtil.stringToDate(date,DateFormats.DATE_TIME_FORMAT_2);
            }
        }
        return null;
    }

    protected abstract void updateWidgetSpecificFields(Recents recent);

    /**
     * case when plan bucket are different if expiry within range show plan bucket having least expiry otherwise
     * show plan bucket having latest txn time
     *
     * @param uniqueMap
     * @param key
     * @param recent
     * @return
     */
    private boolean updateInUniqueMap(Map<String, Recents> uniqueMap, String key, Recents recent) {
        if (!uniqueMap.containsKey(key))
            return true;
        if(Constants.RENT_PAYMENT.equalsIgnoreCase(recent.getKey().getService()) ||
                Constants.TUITION_FEES.equalsIgnoreCase(recent.getKey().getService())){
            if(recent.getRentTFData() ==null && uniqueMap.get(key).getRentTFData()!=null) {
                 recent.setRentTFData(uniqueMap.get(key).getRentTFData());
                 recent.setRentConsent(uniqueMap.get(key).getRentConsent());
             }else if(recent.getRentTFData() !=null && uniqueMap.get(key).getRentTFData()==null) {
                 uniqueMap.get(key).setRentTFData(recent.getRentTFData());
                 uniqueMap.get(key).setRentConsent(recent.getRentConsent());
            }
        }



        if (Constants.FINANCIAL_SERVICES.equalsIgnoreCase(recent.getKey().getService())) {
            Recents recentInMap = uniqueMap.get(key);
            Date latestConsentValidTill = DateUtil.getLatestDate(recentInMap.getConsentValidTill(), recent.getConsentValidTill());
            if (!Objects.equals(recentInMap.getConsentValidTill(), latestConsentValidTill)) {
                recentInMap.setConsentValidTill(latestConsentValidTill);
            }
            if (!Objects.equals(recent.getConsentValidTill(), latestConsentValidTill)) {
                recent.setConsentValidTill(latestConsentValidTill);
            }
        }

        if (uniqueMap.get(key).isOnlyTxn() && recent.isOnlyTxn()) {
            return RecentUtils.compareRecents(uniqueMap.get(key), recent);
        }
        if (!recent.isOnlyTxn() && !uniqueMap.get(key).isOnlyTxn())
            return uniqueMap.get(key).getDueDate().compareTo(recent.getDueDate()) > 0;

        return !recent.isOnlyTxn();
    }

    protected String getTxnMapKey(Recents recent, RecentConfig config) {

        if (Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType())
                || Objects.isNull(recent.getProductId())) {
            return new StringBuilder().append(recent.getKey().getRechargeNumber()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getOperator()).toString();
        }

        else {
            return getUniqueKey(recent, config);
        }
    }

    protected String getTxnMapKeyForNonRU(Recents recent, RecentConfig config) {

        if(Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType()) &&"mobile".equalsIgnoreCase(recent.getKey().getService())) {
            return new StringBuilder().append(recent.getKey().getRechargeNumber()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getOperator()).append(Constants.Delimiters.UNDERSCORE).append(RecentUtils.getPlanBucket(recent)).toString();
        }
        else if (Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType())
                || Objects.isNull(recent.getProductId())) {
            return new StringBuilder().append(recent.getKey().getRechargeNumber()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getOperator()).toString();
        }

        else {
            return getUniqueKey(recent, config);
        }
    }


    protected abstract String getUniqueKey(Recents recent, RecentConfig config);

    protected abstract boolean isSkippable(Recents recent, FrequentOrderRequest request);

    protected void pushCountToDD(String metricName, Integer count, String state) {
        String[] tags = new String[1];
        tags[0] = Constants.MetricConstants.STATE + ":" + state;
        monitoringClient.count(metricName, count, tags);
    }

    private void updateLocalisationConfig(RecentConfig config, Boolean excludeDropoff) {

        if (!Boolean.TRUE.equals(excludeDropoff)) {
            String[] keys = new String[]{"en-IN", "recents", "drop_off_config"};
            String locMessage = localisationManager.getMessage(keys, false, Collections.emptyMap());
            if (locMessage != null && !StringUtils.isEmpty(locMessage)) {
                JSONObject object = new JSONObject(locMessage);
                config.setDropOffConfig(object);
            }
        }

        logger.trace("updateLocalisationConfig ends config is {} ", config);


    }

    /**
     * Check if we neeed to show dropOff or recents for a specific number.
     *
     * @param product
     * @param dropOff
     * @param localisationConfig
     * @param request
     * @param orderId
     * @return
     */
    public boolean isDropOffEligible(ProductMin product, DropOffResponse dropOff, RecentConfig localisationConfig, FrequentOrderRequest request, Long orderId) {
        logger.info("##### 1 : dropOff : {}", dropOff);
        if (dropOff == null)
            return false;
        logger.info("##### 2 : dropOff.getService() : {}", dropOff.getService());
        if (serviceConfig.newAccountServices().contains(dropOff.getService()))
            return false;

        logger.info("##### 3");
        if (orderId != null && UTILITY_PAY_TYPES.contains(product.getPayType()) && VALIDATION_MESSAGE_TYPE.equalsIgnoreCase(dropOff.getType())) {
            return false;
        }
        Date expiryDate = DropOffUtils.getBillDueOrExpiryDate(dropOff);
        logger.info("##### 4");
        if (PREPAID_PAYTYPE.equalsIgnoreCase(product.getPayType()) && VALIDATION_MESSAGE_TYPE.equalsIgnoreCase(dropOff.getType())
                && ((expiryDate != null && isExpiryWithinRange(product.getPayType(), product.getService(), expiryDate)))) {
            return false;
        }


        JSONObject config = localisationConfig.getDropOffConfig();
        logger.info("##### 5 : config : {}", config);
        logger.info("###### ");
        return request.getOnlyReminder() || DropOffUtils.checkForClientVersion(config, request, product.getCategoryId());


    }

    protected boolean isExpiryWithinRange(Date expiryDate, int startDays, int endDays) {
        Date startDate = DateUtil.getZeroTimeDate(DateUtils.addDays(new Date(), -endDays));

        Date endDate = DateUtil.getEndOfDayDate(DateUtils.addDays(new Date(), startDays));

        logger.trace("isExpiryWithinRange  range start {}, end {}, expiryDate {}", startDate, endDate, expiryDate);

        return expiryDate.compareTo(startDate) >= 0 && expiryDate.compareTo(endDate) <= 0;
    }

    protected boolean isExpiryWithinRange(String payType, String service, Date dueDate) {

        if (payType == null || dueDate == null || service == null)
            return false;


        return isExpiryWithinRange(dueDate, getBillVisiblityStartDays(service, payType), getBillVisiblityEndDays(service, payType));

    }

    private void addDropOffInFavResponse(Map<String, DropOffResponse> dropOffUniqueMap, List<FrequentOrderResponse> favResponse, FrequentOrderRequest request, RecentConfig config) {
        logger.info("addDropOffInFavResponse starts dropOffUniqueMap size is {}, favResponse size is {}", dropOffUniqueMap.size(), favResponse.size());

        if (!CollectionUtils.isEmpty(dropOffUniqueMap)) {
            List<FrequentOrderResponse> dropOffFavResponse = dropOffUniqueMap.values().stream()
                    .filter(dropOff -> !serviceConfig.newAccountServices().contains(dropOff.getService()))
                    .filter(dropOff -> !CVRProductCache.getInstance().isInactivePID(Long.valueOf(dropOff.getProduct_id())))
                    .filter(dropOff -> DropOffUtils.checkForClientVersion(config.getDropOffConfig(), request, Long.valueOf(dropOff.getCategory_id())))
                    .filter(dropOff -> filterBasedOnFlag(isExpiryWithinRange(dropOff),request))
                    .map(dropOff -> FrequentOrderResponseBuilder
                            .build(dropOff, null, isExpiryWithinRange(dropOff))).collect(Collectors.toList());
            favResponse.addAll(dropOffFavResponse);
        }

    }

    private int getBillVisiblityStartDays(String service, String payType) {

        Integer allowedLength = serviceConfig.getBillVisiblityStartDaysByService(service);
        if (allowedLength == null || allowedLength.intValue() == 0) {
            if (PayType.PREPAID.value.equalsIgnoreCase(payType))
                return serviceConfig.getPrepaidStartDay();
            else
                return serviceConfig.getPostpaidStartDay();
        }

        return allowedLength;
    }

    private int getBillVisiblityEndDays(String service, String payType) {

        Integer allowedLength = serviceConfig.getBillVisiblityEndDaysByService(service);
        if (allowedLength == null || allowedLength.intValue() == 0) {
            if (PayType.PREPAID.value.equalsIgnoreCase(payType))
                return serviceConfig.getPrepaidEndDay();
            else
                return serviceConfig.getPostpaidEndDay();
        }
        return allowedLength;
    }

    private boolean isRecentBillValid(Recents recent, FrequentOrderRequest request, boolean prepaidTxnExists) {
        return (
                recent.getOrderId() != null
                || prepaidTxnExists
                || (StringUtils.equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE, recent.getPayType()) && Boolean.TRUE.equals(request.getOnlyReminder()) && Boolean.TRUE.equals(serviceConfig.enableSavedCardInReco()))
                || Boolean.TRUE.equals(isRecentValidForAccountCreation(recent))
                || StringUtils.equalsAnyIgnoreCase(recent.getEventSource(),Constants.EVENT_SOURCE.SMART_RECENT_DROPOFF,Constants.EVENT_SOURCE.SMART_RECENT_VALIDATION, UPI_CREDIT_CARD)
                || isRecentValidValidForMobile(recent)) && filterBasedOnFlag(recent, request);

    }

    private boolean isRecentValidValidForMobile(Recents recent){
        return (Constants.SERVICE_MOBILE.equalsIgnoreCase(recent.getKey().getService())
                && RecentUtils.isWhitelistedCustIdForMobileNonRU(recent.getKey().getCustomerId(), serviceConfig)
                && recent.isRecoEvent() && ((!RecentUtils.isNotTxnRecord(recent))|| isDataExhaustRecordForMobile(recent)));
    }

    private boolean isDataExhaustRecordForMobile(Recents recent){
        return DATA_EXHAUST_PLAN_BUCKET.equals(recent.getKey().getPlanBucket());
    }

    private boolean filterBasedOnFlag(boolean isExpiryWithinRange, FrequentOrderRequest request) {
        logger.trace("filterBasedOnFlag isExpiryWithinRange {} {}", isExpiryWithinRange, request.getRecentBillType());
        return !StringUtils.equalsAnyIgnoreCase(request.getRecentBillType(), Constants.BillType.DUE_BILLS) || isExpiryWithinRange;
    }

    private boolean filterBasedOnFlag(Recents recent, FrequentOrderRequest request, boolean expiryWithinRange) {
        return filterBasedOnFlag(expiryWithinRange, request)
                || (Boolean.TRUE.equals(request.getIsReNewSubscription()) && RecentUtils.isAutomaticRenewState(recent));
    }

    private boolean filterBasedOnFlag(Recents recent, FrequentOrderRequest request) {
        return !StringUtils.equalsAnyIgnoreCase(request.getRecentBillType(), Constants.BillType.DUE_BILLS) ||
                (Boolean.TRUE.equals(RecentUtils.isBillDue(recent)) && !recent.isOnlyTxn())
                || (Boolean.TRUE.equals(request.getIsReNewSubscription()) && RecentUtils.isAutomaticRenewState(recent));
    }

    public boolean isValidationRecentWithExpiredValidity(Recents recent) {
        if (Boolean.TRUE.equals(recent.getIsValidation()) && Objects.nonNull(recent.getExtra())) {
            String extra = recent.getExtra();
            JSONObject extraJson = new JSONObject(extra);
            if (extraJson.has(Constants.VALIDITY_EXPIRED) && !extraJson.isNull(Constants.VALIDITY_EXPIRED)) {
                return extraJson.getBoolean(Constants.VALIDITY_EXPIRED);
            }
        }
        return false;
    }

    private Boolean isRecentValidForAccountCreation(Recents recent) {
        if (Boolean.TRUE.equals(serviceConfig.disableDropOffService()) || serviceConfig.newAccountServices().contains(recent.getKey().getService())
                || CommonConsts.PAYTM_POSTPAID_SERVICE.equalsIgnoreCase(recent.getKey().getService()) ||
                isValidationRecentWithExpiredValidity(recent)) {
            if (RecentUtils.getFailedOrderId(recent) != null) {
                return true;
            }
            if (RecentUtils.getPendingOrderId(recent) != null) {
                return Boolean.TRUE.equals(RecentUtils.isValidPendingTxn(recent)) || RecentUtils.isBillDue(recent);                //to handle case when the only txn in recent is of pending, then either it should be within 30 days or else its bill is updated and is due.
            }

            if(recent.getKey().getService().equals(Constants.FINANCIAL_SERVICE) && !skipDateCheck(recent.getKey().getService()) && recent.getOrderId() == null && !Boolean.TRUE.equals(recent.getIsSavedCard()) && Objects.nonNull(recent.getCreatedAt()) && recent.getCreatedAt().before(DateUtil.stringToDate(Constants.CONSUMER_DEPLOYMENT_DATE, DATE_FORMAT))){
                return false;
            }

            return (
                    Boolean.TRUE.equals(recent.getIsValidation()) ||
                            (!Constants.EVENT_SOURCE.SMS.equalsIgnoreCase(recent.getEventSource()) && recent.getOrderId() == null && (!Boolean.TRUE.equals(recent.getIsSavedCard()) || (Objects.nonNull(recent.getEventSource()) && recent.getEventSource().equalsIgnoreCase(Constants.EVENT_SOURCE.PG_DELETED_AMW)) || (Objects.nonNull(recent.getPgCardId()) && recent.getPgCardId().equalsIgnoreCase(recent.getKey().getRechargeNumber()))))
            ) && (
                    skipDateCheck(recent.getKey().getService()) ||
                            recent.getUpdatedAt().after(DateUtil.stringToDate(Constants.CONSUMER_DEPLOYMENT_DATE, DATE_FORMAT))
            );
        }
        return false;
    }
    private boolean skipDateCheck(String service){
        if(Objects.nonNull(service)) {
            if(Constants.RENT_PAYMENT.equalsIgnoreCase(service) || Constants.TUITION_FEES.equalsIgnoreCase(service)){
                return true;
            }
        }
        return false;
    }

    private String getIssuingBankName(Long productId) {

        ProductMin product = CVRProductCache.getInstance().getProductDetails(productId);
        if (Objects.isNull(product)) {
            return null;
        }
        if (!PayType.CREDIT_CARD.value.equalsIgnoreCase(product.getPayType())) {
            return product.getService();
        }

        if (Objects.nonNull(product.getOperatorLabel()) && !product.getOperatorLabel().isEmpty()) {
            return product.getOperatorLabel().toUpperCase();
        }

        return null;
    }

    protected boolean isPartialBillExpiryWithinRange(String payType, String service, Date billDate) {

        if (payType == null || service == null || billDate == null)
            return false;
        Integer allowedDays = serviceConfig.getPartialBillVisibilityDays();
        if(allowedDays == null){
            allowedDays = CommonConsts.DEFAULT_PARTIAL_BILL_EXPIRY_DAYS;
        }
        return isExpiryWithinRange(billDate, 0, allowedDays);

    }

    private boolean isSMSEventSourcePresentForMobile(Recents recent){
        return (Constants.SERVICE_MOBILE.equalsIgnoreCase(recent.getKey().getService())&& Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType()) && RecentUtils.isWhitelistedCustIdForMobileNonRU(recent.getKey().getCustomerId(), serviceConfig) && RecentUtils.isNotTxnRecord(recent) && (Constants.EVENTSOURCE_SMS.equalsIgnoreCase(recent.getEventSource())||Constants.EVENTSOURCE_SMS.equalsIgnoreCase(RecentUtils.getCreatedSourceFromExtra(recent))));
    }

    private void sendEventTypeCombinationMetrics(FrequentOrderResponse response){
        if(Objects.nonNull(response)) {
            List<String> tags = new ArrayList<>();
            tags.add("EventType:" + response.getEventType());
            tags.add("EventState:" + response.getEventState());
            tags.add("BillState:" + response.getBillState());
            metricsHelper.recordSuccessRateForMultipleTags("EVENTS_COMBINATION", tags);
        }
    }


    protected abstract void addSmsCardToFavResponse(List<FrequentOrderResponse> favResponse, FrequentOrderResponse response);

}
