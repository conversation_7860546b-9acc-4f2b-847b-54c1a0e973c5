package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.builder.RecentsBuilder;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.enums.EventState;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.SmartRecents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.util.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.concurrent.*;
import java.util.stream.Collectors;
import com.paytm.saga.util.CommonUtils;

import java.util.*;

import static com.paytm.saga.common.constant.Constants.*;
import static com.paytm.saga.common.constant.Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET;
import static com.paytm.saga.common.constant.Constants.MetricConstants.FREQUENT_ORDER;
import static com.paytm.saga.common.constant.Constants.OLD_BILL_REMINDER_STATUS;
import static com.paytm.saga.common.constant.Constants.ServiceTypeConstants.ELECTRICITY;

@Service
@DependsOn("keyspaceRecentConfig")
public class RecoWidgetDataService extends FrequentOrderServiceImpl {
    @Value("${timeout.reco.cc}")
    private int timeoutRecoCC;
    private final CustomLogger logger = CustomLogManager.getLogger(RecoWidgetDataService.class);
    @Autowired
    private SmartReminderService smartReminderService;
    @Autowired
    private RecentsRepositoryWrapperService recentsRepository;
    @Autowired
    private MetricsHelper metricsHelper;
    @Autowired
    private EligibleRecoRecentsCCImpl eligibleRecoRecentsCC;

    @Override
    protected List<Recents> getRecentDataFromDB(FrequentOrderRequest request) {

        logger.info("RecoWidgetDataService :: getRecentDataFromDB starts");

        List<RecentsPrimaryKey> recentsPrimaryKeys = smartReminderService.getFilteredRecentTableClusterKeys(request);

        logger.trace("filtered recentsPrimaryKeys  is {} ", recentsPrimaryKeys);
        List<Recents> recents = new ArrayList<>();
        if (!CollectionUtils.isEmpty(recentsPrimaryKeys)) {

            logger.info("recentsPrimaryKeys size is {}", recentsPrimaryKeys.size());

            logger.info(" recentsPrimaryKeys is {}", recentsPrimaryKeys);

            recents = dao.findByQuery(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys));

            metricsHelper.pushToDD(FREQUENT_ORDER, Constants.MetricConstants.RECO_RECENT_KEY_COUNT+"_"+recentsPrimaryKeys.size());
            if(recentsPrimaryKeys.size() > serviceConfig.getRecoRecentKeyCountLoggingThreshold()) {
                logger.info("filtered recentsPrimaryKeys breaching threshold are as count " + recentsPrimaryKeys.size() + " and keys are {}", recentsPrimaryKeys);
            }

            logger.info("recents is {}", recents);
        } else {
            logger.info("recentsPrimaryKeys size is empty");
        }

        pushCountToDD(FREQUENT_ORDER, recentsPrimaryKeys.size() - recents.size(), "RECENT_KEYS_TO_RECENT_DIFF");

        return recents;
    }

    @Override
    protected boolean skipBasedOnCategory(Map<Long, Integer> categoryWiseCount, Long productId) {
        return false;
    }

    @Override
    protected void addToFavResponse(List<FrequentOrderResponse> favResponse, FrequentOrderResponse response, Map<Long, Integer> categoryWiseMap) {
        if (response.getBill() != null && Boolean.TRUE.equals(response.getBill().getIsBillDue()))
            favResponse.add(response);
        else if(Boolean.TRUE.equals(response.getIsPrepaid())){
            favResponse.add(response);
        }
        else if(Constants.EVENT_SOURCE.SMART_RECENT_VALIDATION.equalsIgnoreCase(response.getEventState().name()) || Constants.EVENT_SOURCE.SMART_RECENT_DROPOFF.equalsIgnoreCase(response.getEventState().name())){
            favResponse.add(response);
        }

        else if(Objects.nonNull(response.getAutomaticState()) && response.getAutomaticState() == 5){
            if(response.getBill() == null || Boolean.FALSE.equals(response.getBill().getIsBillDue())){
                response.setEventState(EventState.RENEW_AUTOMATIC);
            }

            favResponse.add(response);
        }
    }


    @Override
    protected void updateWidgetSpecificFields(Recents recent) {
        if(Objects.nonNull(recent.getAutomaticStatus()) && recent.getAutomaticStatus() == 5){
            if (!RecentUtils.isBillDue(recent) || !isExpiryWithinRange(recent.getPayType(), recent.getKey().getService(), recent.getDueDate())) {
                if(RecentUtils.isBillDuePartial(recent) && isPartialBillExpiryWithinRange(recent.getPayType(), recent.getKey().getService(), recent.getBillDate())){
                    recent.setRecoEvent(true);
                    return;
                }
                recent.setOnlyTxn(true);
            }
        }

        recent.setRecoEvent(true);
        return;
    }

    @Override
    protected boolean isSkippable(Recents recent, FrequentOrderRequest request) {

        if(recent.getDueDate() != null && recent.getKey().getService().equalsIgnoreCase(ELECTRICITY) && RecentUtils.checkIfPrepaidCase(recent)){
            if(Objects.nonNull(recent.getDueAmount())) {
                Integer prepaidElectricityBillVisibilityOffset = serviceConfig.getPrepaidElectricityBillVisibilityOffset();
                logger.info("isSkippable, low balance prepaid electricity, last visibility day - {}, ", DateUtils.addDays(recent.getDueDate(), prepaidElectricityBillVisibilityOffset));
                return DateUtils.addDays(recent.getDueDate(), prepaidElectricityBillVisibilityOffset).before(new Date());
            } else {
                Integer heuristicPrepaidElectricityBillVisibilityOffset = serviceConfig.getHeuristicPrepaidElectricityBillVisibilityOffset();
                logger.info("isSkippable, heuristic low balance prepaid electricity, last visibility day - {}", DateUtils.addDays(recent.getDueDate(), heuristicPrepaidElectricityBillVisibilityOffset));
                return DateUtils.addDays(recent.getDueDate(), heuristicPrepaidElectricityBillVisibilityOffset).before(new Date());
            }
        }

        Date startDate = DateUtil.getZeroTimeDate(DateUtils.addDays(new Date(), -serviceConfig.getBillVisiblityDays(recent.getKey().getService())));

        logger.trace("isSkippable  startDate {}", startDate);
        if(StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), Constants.EVENT_SOURCE.SMS) && StringUtils.equalsAnyIgnoreCase(recent.getPayType(),"prepaid") && StringUtils.equalsAnyIgnoreCase(recent.getKey().getService(),"mobile") && !(StringUtils.equalsAnyIgnoreCase(RecentUtils.getPlanBucket(recent),"Special Recharge")|| StringUtils.equalsAnyIgnoreCase(RecentUtils.getPlanBucket(recent),DATA_EXHAUST_PLAN_BUCKET)) ){
            return true;
        }

        if(RecentUtils.isNotSpecialRechargeSMSCard(recent)
                && !StringUtils.equalsAnyIgnoreCase(RecentUtils.getPlanBucket(recent),DATA_EXHAUST_PLAN_BUCKET)){
            return true;
        }



        if (Constants.FASTAG_LOW_BALANCE_OPERATOR.equalsIgnoreCase(recent.getOperator())) {
            if(Objects.nonNull(recent.getDueDate())) {
                Integer ttl = TTLExhaustEventUtil.getTTL(recent.getKey().getService(), recent.getDueDate());
                return ttl <= 0;
            }
        }

        if(Objects.nonNull(recent.getAutomaticStatus()) && recent.getAutomaticStatus() == 5){
            logger.trace("not Skippable In Case of renew Automatic subscription Reco Card");
            return false;
        }

        if(Objects.nonNull(recent.getReminderStatus()) && recent.getReminderStatus() == OLD_BILL_REMINDER_STATUS && Objects.nonNull(recent.getOldBillFetchDate())){
            logger.trace("not Skippable In Case of Old Bill flow");
            return false;
        }

        if (Objects.nonNull(recent.getDueDate())) {
            logger.trace("isSkippable  dueDate {}", recent.getDueDate());
            return !(recent.getDueDate().compareTo(startDate) >= 0);
        }

        if(Constants.EVENT_SOURCE.SMART_RECENT_VALIDATION.equalsIgnoreCase(recent.getEventSource()) || Constants.EVENT_SOURCE.SMART_RECENT_DROPOFF.equalsIgnoreCase(recent.getEventSource())){
            logger.trace("not Skippable In Case of SmartRecentData ::: recent.getEventSource() {}", recent.getEventSource());
            return false;
        }

        if(RecentUtils.isBillDuePartial(recent) && isPartialBillExpiryWithinRange(recent.getPayType(), recent.getKey().getService(), recent.getBillDate())){
            return false;
        }

        if(RecentUtils.isBillDueRUPartial(recent, serviceConfig.getRUPartialBillRecoServices(), serviceConfig.getRuPartialBillUpdatedSourceList())) {
            return false;
        }

        return true;
    }

    public String getUniqueKey(Recents recent, RecentConfig config) {
        StringBuilder key = new StringBuilder();
        //ProductMin product = CVRProductCache.getInstance().getProductDetails(recent.getProductId());

        //logger.info("product id {} details {}",recent.getProductId(),product);

        if (Constants.CREDIT_CARD_PAYTYPE.equalsIgnoreCase(recent.getPayType())) {
            if (Objects.nonNull(CVRProductCache.getInstance().getCustomOperatorForCreditCard(recent.getProductId()))) {
                key.append(recent.getMcn()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(CVRProductCache.getInstance().getCustomOperatorForCreditCard(recent.getProductId()));
            } else {
                key.append(recent.getMcn()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getOperator());
            }
        }
        else if (StringUtils.equalsAnyIgnoreCase(recent.getEventSource(),Constants.EVENT_SOURCE.SMART_RECENT_VALIDATION) || StringUtils.equalsAnyIgnoreCase(recent.getEventSource(),Constants.EVENT_SOURCE.SMART_RECENT_DROPOFF)) {
            key.append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getOperator());
        }
        /*else if(recent.getKey().getService().equalsIgnoreCase(com.paytm.saga.enums.Service.ELECTRICITY.value) && (serviceConfig.getWhielistOperatorServicesForDedup().contains(recent.getKey().getOperator().toLowerCase()))) {
            key.append(RecentUtils.getTrimmedRechargeNumber(recent.getKey().getRechargeNumber())).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService());

        }*/
        else if (config.getIncludeOperatorInKeyServices().contains(recent.getKey().getService())) {
            key.append(recent.getKey().getRechargeNumber()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getOperator()).append(recent.getKey().getPlanBucket());
        } else if(recent.getKey().getService().equals("mobile")){
            key.append(recent.getKey().getRechargeNumber()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getOperator()).append(Constants.Delimiters.UNDERSCORE).append(RecentUtils.getPlanBucket(recent));

        }else
            key.append(recent.getKey().getRechargeNumber()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(recent.getKey().getPlanBucket());


        return key.toString();
    }

    protected void updateSortedRecentsAndDropoff(
        List<Recents> recents,
        Map<String, DropOffResponse> dropOffMap,
        FrequentOrderRequest request,
        FrequentOrderResponseWrapper wrapper
    ) throws RechargeSagaBaseException {
        List<Future<Object>> recentAndDropOffTaskFuture = new ArrayList<>();
        List<Future<List<Recents>>> recentsForCCFuture = new ArrayList<>();
        FrequentOrderRequest requestCloneRequest = new FrequentOrderRequest();
        requestCloneRequest.setCustomerId(request.getCustomerId());
        ArrayList services = new ArrayList<>();
        services.add("electricity");
        requestCloneRequest.setServices(services);
        recentAndDropOffTaskFuture.add(frequentOrderSmartRecentExecutor.submit(() -> {
            if (requestCloneRequest.getServices() != null && !requestCloneRequest.getServices().isEmpty() && serviceConfig.getSmartRecentRecoEnabledServices().contains(requestCloneRequest.getServices().get(0))) {
                return smartRecentsService.getSmartRecentsForFrequentOrderCLP(requestCloneRequest);
            }
            return null;
        }));
        boolean isCCDataEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(request.getCustomerId());
        String loggerService = LoggerThreadContext.getServiceName();
        //for credit card only
        if (isCCDataEncryptionRequired && (request.getServices().isEmpty() || CommonUtils.containIgnoreCase(request.getServices(), FINANCIAL_SERVICE))) {
            recentsForCCFuture.add(frequentOrderCCRecoExecutor.submit(() -> {
                LoggerThreadContext.setServiceName(loggerService);
                try{
                    return recentsRepository.findByCustomerIdAndService(request.getCustomerId(), FINANCIAL_SERVICE);
                } finally {
                    LoggerThreadContext.clear();
                }
            }));
        }
        List<Recents> recent;
        try {
            recent = getRecentDataFromDB(request);
        } catch (Exception ex) {
            logger.error("updateSortedRecentsAndDropoff  ExecutionException is {} recents {} ", ex, recents);
            throw new RechargeSagaBaseException(ex.getMessage());
        }
        try {
            //for credit card only
            if (isCCDataEncryptionRequired && !recentsForCCFuture.isEmpty()) {
                List<Recents> recentsForCC = recentsForCCFuture.get(0).get(serviceConfig.getFrequentOrderCCRecoExecutorTimeout(timeoutRecoCC), TimeUnit.MILLISECONDS);
                if (Objects.nonNull(recentsForCC)) {
                    recent.addAll(eligibleRecoRecentsCC.getEligibleRecoRecents(recentsForCC));
                }
            }
            wrapper.setCCTimedOut(false);
        } catch (TimeoutException ex) {
            for (Future<List<Recents>> future : recentsForCCFuture) {
                future.cancel(true);
            }
            wrapper.setCCTimedOut(true);
            metricsHelper.pushToDD(Constants.MetricConstants.RECENT_CC, Constants.MetricConstants.RECENT_CC_TIMEOUT);
            logger.error("updateSortedRecentsAndDropoff TimeoutException for CC for customer_id {} is  ", request.getCustomerId(),ex);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            logger.error("updateSortedRecentsAndDropoff InterruptedException for CC for customer_id {} is ",request.getCustomerId(), ex);
        } catch (ExecutionException ex) {
            logger.error("updateSortedRecentsAndDropoff : Task execution failure for customer_id {} : {}",request.getCustomerId(), ex.getMessage());
        }
        try {
            if (recent != null) {

                recent.stream().forEach(recentObj -> recentObj.setUpdatedAt(ObjectUtils.firstNonNull(recentObj.getUpdatedAt(), recentObj.getTxnTime())));
                recents.addAll(CommonUtils.collectionToStream(recent).filter(recentObj -> recentObj.getUpdatedAt() != null && filterForSMSEnabledService(recentObj, request)).collect(Collectors.toList()));
                Collections.sort(recents, Comparator.comparing(Recents::getUpdatedAt, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
            }
        } catch (Exception ex) {
            logger.error("updateSortedRecentsAndDropoff  ExecutionException is {} recents {} ", ex, recents);
            throw new RechargeSagaBaseException(ex.getMessage());
        }
        try {
            if (!Boolean.TRUE.equals(request.getExcludeDropoff())) {
                Map<String, DropOffResponse> dropOffResponse = dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null);
                if (dropOffResponse != null)
                    dropOffMap.putAll(dropOffResponse);

            }
        } catch (Exception ex) {
            logger.error("updateSortedRecentsAndDropoff  ExecutionException is {} recents {} dropOffMap {}", ex, recents, dropOffMap);
        }
        try {
            List<SmartRecents> smartRecent = (List<SmartRecents>) recentAndDropOffTaskFuture.get(0).get(serviceConfig.getFrequentOrderExecutorTimeOut(), TimeUnit.MILLISECONDS);

            if (smartRecent != null && !CollectionUtils.isEmpty(smartRecent)) {
                Collections.sort(smartRecent, Comparator.comparing(SmartRecents::getUpdatedAt, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                recents.addAll(smartRecent.stream().map(RecentsBuilder.fromSmartRecents::apply).collect(Collectors.toList()));
            }
        } catch (TimeoutException ex) {
            logger.error("updateSortedRecentsAndDropoff TimeoutException is {} recents {}", ex, recents);
            throw new RechargeSagaBaseException(ex.getMessage());
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            logger.error("updateSortedRecentsAndDropoff InterruptedException is {} recents {} ", ex, recents);
            throw new RechargeSagaBaseException(ex.getMessage());
        } catch (ExecutionException ex) {
            logger.error("updateSortedRecentsAndDropoff  ExecutionException is {} recents {} ", ex, recents);
            throw new RechargeSagaBaseException(ex.getMessage());
        }
        logger.trace("recents is {} and dropOffMap is {}", recents, dropOffMap);
    }


    @Override
    protected void addSmsCardToFavResponse(List<FrequentOrderResponse> favResponse, FrequentOrderResponse response) {
        if (response.getBill() != null && Boolean.TRUE.equals(response.getBill().getIsBillDue()))
            favResponse.add(response);
        else if(Boolean.TRUE.equals(response.getIsPrepaid())){
            favResponse.add(response);
        }
    }

    @Override
    protected void handleFastagRechargeNumber(List<Recents> recents) {
        return;
    }

    @Override
    protected void revertFastagRechargeNumber(List<Recents> recents, Map<String, Recents> filteredRecents, Map<String, Recents> txnStatus, Map<String, Recents> txnStatusForNonRU, RecentConfig config) {
        return;
    }
}
