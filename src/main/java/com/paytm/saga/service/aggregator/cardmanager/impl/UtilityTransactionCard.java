package com.paytm.saga.service.aggregator.cardmanager.impl;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.DeepLinkTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.service.aggregator.cardmanager.CardManager;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import com.paytm.saga.util.ChannelHistoryUtils;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.OMSToRechargeStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.paytm.saga.common.constant.UtilityThemeTypes.*;

@Component("utilityTransactionCard")
public class UtilityTransactionCard implements CardManager {

    private static final Logger logger = LogManager.getLogger(UtilityTransactionCard.class);

    private ViewManager ctaManager;
    private ViewManager displayValuesManager;
    private ViewManager headersViewManager;

    private ViewManager childCtaManager;


    @Autowired
    public UtilityTransactionCard(final @NotNull @Qualifier("ctaViewManager") ViewManager ctaManager,
                                  final @NotNull @Qualifier("childCtaViewManager") ViewManager childCtaManager,
                                  final @NotNull @Qualifier("displayValuesViewManager") ViewManager displayValuesManager,
                                  final @NotNull @Qualifier("headersViewManager") ViewManager headersViewManager) {
        this.ctaManager = ctaManager;
        this.displayValuesManager = displayValuesManager;
        this.headersViewManager = headersViewManager;
        this.childCtaManager = childCtaManager;
    }


    private static final Map<String, String> omsStatusMap = new HashMap<String, String>() {
        {
            put("SUCCESS", "success");
            put("CANCEL", "cancelled");
            put("REVERSAL_FAILURE", "failed");
            put("PAYMENT_FAILURE", "failed");

            put("FAILURE", "failed");
            put("PAYMENT_PENDING", "pending");
            put("PENDING", "pending");
        }
    };
    private static final Map<String, String> themeTypeMap = new HashMap<String, String>() {
        {
            put("recharge_success", RECHARGE_SUCCESS);
            put("recharge_failed", RECHARGE_FAILED);
            put("recharge_pending", RECHARGE_PENDING);
            put("recharge_cancelled", RECHARGE_CANCELLED);

            put("bill_success", BILL_SUCCESS);
            put("bill_failed", BILL_FAILED);
            put("bill_pending", BILL_PENDING);
            put("bill_cancelled", BILL_CANCELLED);

            put("automatic_success", AUTOMATIC_PAYMENT_SUCCESS);
            put("automatic_failed", AUTOMATIC_PAYMENT_FAILED);
            put("automatic_pending", AUTOMATIC_PAYMENT_PENDING);
            put("automatic_cancelled", AUTOMATIC_PAYMENT_CANCELLED);


        }
    };

    @Override
    public HistoryView getCardInfo(CardInfoDto cardInfoDto) {
        ChannelHistory channelHistory = cardInfoDto.getChannelHistory();
        HistoryView historyView = new HistoryView();
        historyView.setNative(cardInfoDto.isNative());
        historyView.setUtility(Boolean.TRUE);
        historyView.setEventDate(channelHistory.getTransactionTime());
        historyView.setCreatedDate(
                DateUtil.dateFormatter(channelHistory.getTransactionTime(), DateFormats.DATE_TIME_FORMAT));
        try {
            historyView.setAmount(channelHistory.getAmount() != null ?
                    Double.parseDouble(channelHistory.getAmount()) : 0.0);
        } catch (NumberFormatException e) {
            logger.error("Invalid amount format in channelHistory: {}", channelHistory.getAmount(), e);
            historyView.setAmount(0.0);
        }
        historyView.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistory.getStatus(),
                channelHistory.getInResponseCode(), channelHistory.getPaymentStatus()));
        historyView.setOrderId(channelHistory.getOrderId());
        // historyView.setPlanBucket(channelHistory.getPlanbucket());
        historyView.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK));
        historyView.setPlanDetail(channelHistory.getDisplayValues());
        historyView.setEventDate(channelHistory.getTransactionTime());
        historyView.setCircle(channelHistory.getCircle());
        historyView.setOperator(channelHistory.getOperator());
        historyView.setService(channelHistory.getService());
        historyView.setPayType(channelHistory.getPaytype());
        historyView.setEventType(channelHistory.getEventType());
        historyView.setStatusCode(channelHistory.getStatus());
        if (cardInfoDto.getThemeType() == null)
            historyView.setThemeType(getRechargeThemeType(channelHistory));
        else
            historyView.setThemeType(cardInfoDto.getThemeType());
        historyView.setOperatorLabel(ChannelHistoryUtils.getOperatorLabel(channelHistory));
        historyView.setBillDate(ChannelHistoryUtils.getBillDate(channelHistory));
        historyView.setDueDate(ChannelHistoryUtils.getDueDate(channelHistory));
        historyView.setPaymentStatus(channelHistory.getPaymentStatus());
        historyView.setMinimumDue(ChannelHistoryUtils.getMinDueAmount(channelHistory));
        // setting all header, display values based on themeTypes
        ChannelHistoryUtils.updateHeadersAndFooters(historyView, headersViewManager.getViewElements(historyView));
        historyView.setDisplayValues(displayValuesManager.getViewElements(historyView));


        if (!cardInfoDto.isIgnoreCTA())
            historyView.setCta(ctaManager.getViewElements(historyView));

        try {
            if (historyView.getDueDate() != null) {
                historyView.setDueDate(DateUtil.dateFormatter(
                        DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2),
                        DateFormats.DATE_FORMAT_2));
            }

            if (historyView.getBillDate() != null) {
                historyView.setBillDate(DateUtil.dateFormatter(
                        DateUtil.stringToDate(historyView.getBillDate(), DateFormats.DATE_TIME_FORMAT_2),
                        DateFormats.DATE_FORMAT_2));
            }
        } catch (RuntimeException e) {
            logger.error("Getting exception in parsing billdate duedate in recharge card");
        }
        return historyView;
    }

    private String getRechargeThemeType(ChannelHistory channelHistory) {

        String status = OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistory.getStatus(),
                channelHistory.getInResponseCode(), channelHistory.getPaymentStatus());
        logger.trace("order id {} , event type {} , status {}", channelHistory.getOrderId(), channelHistory.getEventType(), status);

        String prefixThemeMapKey = "bill";

        if (Constants.CommonConstants.PREPAID_PAYTYPE.equalsIgnoreCase(channelHistory.getPaytype())) {
            prefixThemeMapKey = "recharge";
        } else {
            if (ChannelHistoryUtils.isAutomaticTransaction(channelHistory))
                prefixThemeMapKey = "automatic";
        }
        String key = prefixThemeMapKey + "_" + omsStatusMap.get(status);
        logger.debug("ThemeTypeMap key is {}", key);
        return themeTypeMap.get(key);
    }


}
