package com.paytm.saga.common.constant;

public class ServiceNameConstants {
    public static final String AGENT_IDENTIFICATION_LIMIT = "agentIdentificationLimit";
    public static final String RECHARGE_NUMBER_TO_CUSTOMER_ID_LIMIT = "rechargeNumberToCustomerIdLimit";
    public static final String SMART_REMINDER_PREPAID_END_DAYS = "smartReminderPrepaidEndDays";
    public static final String SMART_REMINDER_PREPAID_START_DAYS = "smartReminderPrepaidStartDays";
    public static final String SMART_REMINDER_POSTPAID_END_DAYS = "smartReminderPostpaidEndDays";
    public static final String SMART_PAYTM_POSTPAID_END_DAYS = "smartPaytmPostpaidEndDays";
    public static final String SMART_RECO_END_DAYS = "smartRecoEndDays";
    public static final String SMART_REMINDER_POSTPAID_START_DAYS = "smartReminderPostpaidStartDays";
    public static final String BILL_END_RANGE_CURRENT_DATE = "billEndRangeFromCurrentDate";
    public static final String PREPAID_ELECTRICITY_BILL_VISIBILITY_OFFSET = "prepaidElectricityBillVisibilityOffset";
    public static final String HEURISTIC_PREPAID_ELECTRICITY_BILL_VISIBILITY_OFFSET = "heuristicPrepaidElectricityBillVisibilityOffset";
    public static final String PREPAID_ALLOWED_SERVICES = "prepaidAllowedServices";
    public static final String PREPAID_ALLOWED_OPERATORS = "prepaidAllowedOperators";
    public static final String REMINDER_BILL_GEN_STATUS_LIST = "reminderBillGenStatusList";
    public static final String FREQUENT_ORDER_EXECUTOR_TIMEOUT = "frequentOrderExecutorTimeout";
    public static final String RECENT_DB_READ_TIMEOUT = "recentDBReadTimeout";
    public static final String END_DAYS = "endDays";
    public static final String START_DAYS = "startDays";
    public static final String SMS_ENABLED_SERVICE = "smsEnabledServices";
    public static final String ENABLE_SMS_CARD_IN_RECENT = "enableSMSCardInRecent";
    public static final String VALIDATION_BLOCK_FOR_CDC = "validationBlockForCDC";
    public static final String ENABLE_SAVED_CARD_IN_RECO = "enableSavedCardInReco";
    public static final String PAYMODE_COUNT_SUPPORT_SERVICE="paymodeCountSupportService";
    public static final String FAILED_IN_RESPONSE_STATES_SERVICE="failedInResponseStatesService";
    public static final String NEW_ACCOUNT_SERVICES="newAccountServices";
    public static final String  WHITELIST_OPERATOR_SERVICE_FOR_DEDUP="whitelistOperatorServiceForDedup";
    public static final String DISABLE_DROPOFF_SERVICE="disableDropOffService";
    public static final String ENABLE_ACC_CREATION_IN_RECENT="enableAccountCreationInRecent";
    public static final String SMS_BILL_UPDATE_SERVICES = "smsBillUpdateServices";

    public static final String CATEGORY_MAPPING = "categoryMapping";
    public static final String EXCLUDE_RECENT_SERVICES = "excludeRecentServices";

    public static final String IS_SMS_LIVE = "isSMSLive";
    public static final String SMART_RECENTS_ENABLED_SERVICES = "smartRecentsEnabledServices";
    public static final String SMART_RECENTS_RECO_ENABLED_SERVICES = "smartRecentsRecoEnabledServices";
    public static final String PARTIAL_BILL_RECENT_EXPIRY_DAYS = "partialBillRecentExpiryDays";

    public static final String CONSUMER_NAME_ALLOWED_SERVICES = "consumerNameAllowedServices";
    public static final String OLD_BILL_PENALTY_DAYS = "oldBillPenaltyDays";
    public static final String OLD_BILL_PENALTY_PERCENTAGE_OPERATOR_WISE = "oldBillPenaltyPercentageOperatorWise";
    public static final String OLD_BILL_PENALTY_PERCENTAGE_SERVICE_WISE = "oldBillPenaltyPercentageServiceWise";
    public static final String OLD_BILL_ENABLED_SERVICES = "oldBillEnabledServices";
    public static final String OLD_BILL_DISABLED_OPERATORS = "oldBillDisabledOperators";

    public static final String WHITELISTED_CUSTIDS_FOR_CDC_RECOVERY="whitelistedCustIdsDorCDCRecovery";
    public static final String PERCENT_LIVE_TRAFFIC="percentForLiveTraffic";
    public static final String REDIRECTION_TO_ROLLBACK_TOPIC="redirectionToRollbackTopic";
    public static final String WHITELISTED_CUSTIDS_FOR_MOBILE_NON_RU="whitelistedCustIdsForMobileNonRU";
    public static final String PERCENT_LIVE_TRAFFIC_FOR_MOBILE_NON_RU="percentForLiveTrafficForMobileNonRU";
    public static final String WHITELISTED_CUSTIDS_FOR_MOBILE_NON_RU_PERSIST="whitelistedCustIdsForMobileNonRuPersist";
    public static final String PERCENT_LIVE_TRAFFIC_FOR_MOBILE_NON_RU_PERSIST="percentForLiveTrafficForMobileNonRuPersist";

    public static final String TIME_DIFF_THRESHOLD_BETWEEN_RECENTS_AND_CDC_KEY= "timeDiffThresholdBetweenRecentsAndCDC";
    public static final String PREPAID_SERVICES_ELIGIBLE_FOR_NONRU_VALIDATION_KEY = "prepaidServicesEligibleForNonRuValidation";
    public static final String AIRTEL_PREPAID_CSV_RECENT_ENABLED="airtelPrepaidCSVRecentEnabled";
    public static final String SYSTEM_FETCH_BILL_SOURCES="systemFetchBillSources";
    public static final String BILL_SOURCES_SERVICES="billSourcesServices";
    public static final String SMS_BILL_SOURCES="smsBillSources";
    public static final String BILL_SOURCE_SYSTEM_FETCH="systemFetch";
    public static final String BILL_SOURCE_SMS_PARSING="smsParsing";
    public static final String BILL_SOURCE_UNKNOWN="unknown";
    public static final String  CC_RECO_EXECUTOR_TIMEOUT="cc_reco_executor_timeout";

    public static final String MOBILE_SUGGESTED_CARDS = "mobileSuggestedCards";
    public static final String ENABLE_SUGGESTED_CARDS = "enableSuggestedCards";
    public static final String PREPAID_ELECTRICITY_LOW_BALANCE_THRESHOLD = "prepaidElectricityLowBalanceThreshold";
    public static final String DUMMY_RECHARGE_NUMBER_ALLOWED_SERVICES = "dummyRechargeNumberAllowedServices";
    public static final String PREPAID_SERVICES = "prepaidAllowedServices";
    public static final String PREPAID_OPERATORS = "prepaidAllowedOperators";
    public static final String ALLOWED_CHAT_HISTORY_SERVICES = "allowedChatHistoryServices";
}
